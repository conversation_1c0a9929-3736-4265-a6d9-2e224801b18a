const AWS = require('aws-sdk');
const Joi = require('joi');
const { logger, ValidationError, InternalServerError } = require('./api');
const { leadService } = require('../models/lead');
const { campaignService } = require('../models/campaign');

// Initialize AWS services
const cloudwatch = new AWS.CloudWatch({
  region: process.env.AWS_REGION || 'us-east-1'
});

// Analytics validation schemas
const metricsQuerySchema = Joi.object({
  startDate: Joi.date().optional().default(() => new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)),
  endDate: Joi.date().optional().default(() => new Date()),
  granularity: Joi.string().valid('hour', 'day', 'week', 'month').default('day'),
  metrics: Joi.array().items(Joi.string()).optional(),
  filters: Joi.object().optional()
});

const reportSchema = Joi.object({
  reportType: Joi.string().valid('campaign', 'lead', 'performance', 'conversion').required(),
  startDate: Joi.date().optional(),
  endDate: Joi.date().optional(),
  filters: Joi.object().optional(),
  groupBy: Joi.array().items(Joi.string()).optional(),
  format: Joi.string().valid('json', 'csv', 'pdf').default('json')
});

class AnalyticsService {
  constructor() {
    this.namespace = 'AI_SDR';
  }

  /**
   * Get campaign metrics with validation
   * @param {Object} queryData - Query parameters
   * @returns {Promise<Object>} Campaign metrics
   */
  async getCampaignMetrics(queryData = {}) {
    // Validate input
    const { error, value } = metricsQuerySchema.validate(queryData);
    if (error) {
      throw new ValidationError('Invalid metrics query', error.details);
    }

    const { startDate, endDate, granularity, metrics, filters } = value;

    logger.info('Fetching campaign metrics', {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      granularity
    });

    try {
      // Get metrics from CloudWatch
      const cloudWatchMetrics = await this.getCloudWatchMetrics(startDate, endDate, granularity);

      // Get database metrics
      const databaseMetrics = await this.getDatabaseMetrics(startDate, endDate, filters);

      // Combine and calculate derived metrics
      const combinedMetrics = this.combineMetrics(cloudWatchMetrics, databaseMetrics);

      logger.info('Campaign metrics retrieved successfully');

      return combinedMetrics;
    } catch (error) {
      logger.error('Failed to get campaign metrics', { error: error.message });
      throw new InternalServerError('Failed to retrieve campaign metrics');
    }
  }

  /**
   * Get metrics from CloudWatch
   */
  async getCloudWatchMetrics(startDate, endDate, granularity) {
    try {
      const period = this.getPeriodFromGranularity(granularity);

      const params = {
        MetricDataQueries: [
          {
            Id: 'emailsSent',
            MetricStat: {
              Metric: {
                Namespace: this.namespace,
                MetricName: 'EmailsSent'
              },
              Period: period,
              Stat: 'Sum'
            }
          },
          {
            Id: 'emailsOpened',
            MetricStat: {
              Metric: {
                Namespace: this.namespace,
                MetricName: 'EmailsOpened'
              },
              Period: period,
              Stat: 'Sum'
            }
          },
          {
            Id: 'emailsClicked',
            MetricStat: {
              Metric: {
                Namespace: this.namespace,
                MetricName: 'EmailsClicked'
              },
              Period: period,
              Stat: 'Sum'
            }
          },
          {
            Id: 'emailsReplied',
            MetricStat: {
              Metric: {
                Namespace: this.namespace,
                MetricName: 'EmailsReplied'
              },
              Period: period,
              Stat: 'Sum'
            }
          },
          {
            Id: 'smsSent',
            MetricStat: {
              Metric: {
                Namespace: this.namespace,
                MetricName: 'SMSSent'
              },
              Period: period,
              Stat: 'Sum'
            }
          }
        ],
        StartTime: startDate,
        EndTime: endDate
      };

      const data = await cloudwatch.getMetricData(params).promise();

      return {
        emailsSent: this.sumMetricValues(data.MetricDataResults.find(r => r.Id === 'emailsSent')),
        emailsOpened: this.sumMetricValues(data.MetricDataResults.find(r => r.Id === 'emailsOpened')),
        emailsClicked: this.sumMetricValues(data.MetricDataResults.find(r => r.Id === 'emailsClicked')),
        emailsReplied: this.sumMetricValues(data.MetricDataResults.find(r => r.Id === 'emailsReplied')),
        smsSent: this.sumMetricValues(data.MetricDataResults.find(r => r.Id === 'smsSent'))
      };
    } catch (error) {
      logger.warn('Failed to get CloudWatch metrics, using fallback', { error: error.message });
      return {
        emailsSent: 0,
        emailsOpened: 0,
        emailsClicked: 0,
        emailsReplied: 0,
        smsSent: 0
      };
    }
  }

  /**
   * Get metrics from database
   */
  async getDatabaseMetrics(startDate, endDate, filters = {}) {
    try {
      // Get campaign statistics
      const campaigns = await campaignService.getCampaigns({
        filter: {
          ...filters,
          createdAt: { $gte: startDate.toISOString(), $lte: endDate.toISOString() }
        }
      });

      // Get lead statistics
      const leads = await leadService.getLeads({
        filter: {
          ...filters,
          createdAt: { $gte: startDate.toISOString(), $lte: endDate.toISOString() }
        }
      });

      return {
        totalCampaigns: campaigns.campaigns.length,
        activeCampaigns: campaigns.campaigns.filter(c => c.status === 'active').length,
        completedCampaigns: campaigns.campaigns.filter(c => c.status === 'completed').length,
        totalLeads: leads.leads.length,
        qualifiedLeads: leads.leads.filter(l => l.score >= 70).length,
        convertedLeads: leads.leads.filter(l => l.emailStatus === 'replied').length,
        leadsByStatus: this.groupLeadsByStatus(leads.leads),
        leadsBySource: this.groupLeadsBySource(leads.leads)
      };
    } catch (error) {
      logger.error('Failed to get database metrics', { error: error.message });
      return {
        totalCampaigns: 0,
        activeCampaigns: 0,
        completedCampaigns: 0,
        totalLeads: 0,
        qualifiedLeads: 0,
        convertedLeads: 0,
        leadsByStatus: {},
        leadsBySource: {}
      };
    }
  }

  /**
   * Combine CloudWatch and database metrics
   */
  combineMetrics(cloudWatchMetrics, databaseMetrics) {
    const totalSent = cloudWatchMetrics.emailsSent + cloudWatchMetrics.smsSent;

    return {
      // Raw metrics
      ...cloudWatchMetrics,
      ...databaseMetrics,

      // Calculated metrics
      totalOutreach: totalSent,
      openRate: totalSent > 0 ? (cloudWatchMetrics.emailsOpened / cloudWatchMetrics.emailsSent * 100) : 0,
      clickRate: cloudWatchMetrics.emailsSent > 0 ? (cloudWatchMetrics.emailsClicked / cloudWatchMetrics.emailsSent * 100) : 0,
      replyRate: totalSent > 0 ? (cloudWatchMetrics.emailsReplied / totalSent * 100) : 0,
      conversionRate: databaseMetrics.totalLeads > 0 ? (databaseMetrics.convertedLeads / databaseMetrics.totalLeads * 100) : 0,
      qualificationRate: databaseMetrics.totalLeads > 0 ? (databaseMetrics.qualifiedLeads / databaseMetrics.totalLeads * 100) : 0,

      // Timestamps
      generatedAt: new Date().toISOString(),
      period: {
        start: new Date().toISOString(),
        end: new Date().toISOString()
      }
    };
  }

  /**
   * Generate analytics report
   * @param {Object} reportData - Report configuration
   * @returns {Promise<Object>} Generated report
   */
  async generateReport(reportData) {
    // Validate input
    const { error, value } = reportSchema.validate(reportData);
    if (error) {
      throw new ValidationError('Invalid report configuration', error.details);
    }

    const { reportType, startDate, endDate, filters, groupBy, format } = value;

    logger.info('Generating analytics report', { reportType, format });

    try {
      let reportData;

      switch (reportType) {
        case 'campaign':
          reportData = await this.generateCampaignReport(startDate, endDate, filters, groupBy);
          break;
        case 'lead':
          reportData = await this.generateLeadReport(startDate, endDate, filters, groupBy);
          break;
        case 'performance':
          reportData = await this.generatePerformanceReport(startDate, endDate, filters);
          break;
        case 'conversion':
          reportData = await this.generateConversionReport(startDate, endDate, filters);
          break;
        default:
          throw new ValidationError(`Unsupported report type: ${reportType}`);
      }

      // Format report based on requested format
      const formattedReport = await this.formatReport(reportData, format);

      logger.info('Analytics report generated successfully', { reportType, format });

      return formattedReport;
    } catch (error) {
      logger.error('Failed to generate report', { reportType, error: error.message });
      throw new InternalServerError('Failed to generate analytics report');
    }
  }

  /**
   * Publish metric to CloudWatch
   * @param {string} metricName - Metric name
   * @param {number} value - Metric value
   * @param {Object} dimensions - Metric dimensions
   */
  async publishMetric(metricName, value, dimensions = {}) {
    try {
      const params = {
        Namespace: this.namespace,
        MetricData: [
          {
            MetricName: metricName,
            Value: value,
            Unit: 'Count',
            Timestamp: new Date(),
            Dimensions: Object.entries(dimensions).map(([Name, Value]) => ({ Name, Value }))
          }
        ]
      };

      await cloudwatch.putMetricData(params).promise();

      logger.debug('Metric published to CloudWatch', { metricName, value, dimensions });
    } catch (error) {
      logger.error('Failed to publish metric', { metricName, value, error: error.message });
      // Don't throw error to avoid breaking main flow
    }
  }

  /**
   * Helper methods
   */
  getPeriodFromGranularity(granularity) {
    switch (granularity) {
      case 'hour': return 3600;
      case 'day': return 86400;
      case 'week': return 604800;
      case 'month': return 2592000;
      default: return 86400;
    }
  }

  sumMetricValues(metricResult) {
    return metricResult?.Values?.reduce((a, b) => a + b, 0) || 0;
  }

  groupLeadsByStatus(leads) {
    return leads.reduce((acc, lead) => {
      const status = lead.emailStatus || 'unknown';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});
  }

  groupLeadsBySource(leads) {
    return leads.reduce((acc, lead) => {
      const source = lead.source || 'unknown';
      acc[source] = (acc[source] || 0) + 1;
      return acc;
    }, {});
  }

  async generateCampaignReport(startDate, endDate, filters, groupBy) {
    // Implementation for campaign report
    return { type: 'campaign', data: [] };
  }

  async generateLeadReport(startDate, endDate, filters, groupBy) {
    // Implementation for lead report
    return { type: 'lead', data: [] };
  }

  async generatePerformanceReport(startDate, endDate, filters) {
    // Implementation for performance report
    return { type: 'performance', data: [] };
  }

  async generateConversionReport(startDate, endDate, filters) {
    // Implementation for conversion report
    return { type: 'conversion', data: [] };
  }

  async formatReport(reportData, format) {
    switch (format) {
      case 'json':
        return reportData;
      case 'csv':
        // Convert to CSV format
        return { format: 'csv', data: 'CSV data would go here' };
      case 'pdf':
        // Convert to PDF format
        return { format: 'pdf', data: 'PDF data would go here' };
      default:
        return reportData;
    }
  }
}

// Export singleton instance
const analyticsService = new AnalyticsService();

module.exports = {
  AnalyticsService,
  analyticsService,
  // Legacy export for backward compatibility
  getCampaignMetrics: () => analyticsService.getCampaignMetrics()
};