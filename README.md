# AI SDR Agent for Mining Equipment Suppliers

An intelligent Sales Development Representative (SDR) agent designed to automate lead generation, qualification, outreach, and analytics specifically for mining equipment suppliers targeting mid-tier mining companies.

## 🎯 Project Overview

The AI SDR Agent is a modular SaaS platform that leverages artificial intelligence to streamline sales processes for mining equipment suppliers. It automates the identification and engagement of potential customers in the mining industry, with a focus on companies with revenues of $10-50 million in regions like Africa and North America.

### Key Features

- **Automated Lead Generation**: Intelligent data collection from public mining industry sources
- **AI-Powered Lead Scoring**: Advanced scoring algorithms to identify high-potential prospects
- **Personalized Outreach**: AI-generated, customized communication for each lead
- **Multi-Channel Communication**: Email, SMS, and LinkedIn outreach capabilities
- **Advanced Analytics**: Comprehensive dashboards and predictive analytics
- **CRM Integration**: Seamless integration with major CRM platforms

## 🏗️ Architecture

The system employs a serverless, microservices architecture built on AWS, ensuring scalability and cost efficiency:

- **Frontend**: React.js with Tailwind CSS
- **Backend**: AWS Lambda functions with API Gateway
- **Database**: AWS DynamoDB for NoSQL data storage
- **AI Services**: Relevance AI and Google Gemini API
- **Communication**: Send<PERSON>rid (email), Twilio (SMS), LinkedIn API
- **Infrastructure**: AWS CloudFormation for Infrastructure as Code

## 📁 Repository Structure

```
AI_SDR/
├── docs/                           # Project documentation
│   ├── planning/                   # Project planning documents
│   │   ├── work-breakdown-structure.md
│   │   ├── implementation-plan.md
│   │   ├── task-list.md
│   │   ├── project-tickets.md
│   │   ├── team-assignments.md
│   │   └── coding-agent-rules.md
│   ├── requirements/               # Requirements and specifications
│   │   ├── product-requirements.md
│   │   ├── software-requirements.md
│   │   ├── product-description.md
│   │   ├── target-audience.md
│   │   └── features-overview.md
│   ├── architecture/               # Technical architecture
│   │   ├── backend-structure.md
│   │   ├── frontend-guidelines.md
│   │   ├── application-flow.md
│   │   ├── tech-stack.md
│   │   └── directory-structure.md
│   ├── strategy/                   # Strategic analysis
│   │   ├── job-analysis.md
│   │   ├── mining-equipment-strategy.md
│   │   ├── implementation-strategy.md
│   │   └── strategic-analysis.md
│   └── implementation/             # Implementation guides
│       ├── advanced-implementation-guide.md
│       ├── building-guide.md
│       ├── strategic-guide-transcript.md
│       └── product-architecture.pdf
├── src/                           # Source code
│   ├── backend/                   # Backend services
│   ├── frontend/                  # Frontend application
│   └── shared/                    # Shared utilities
├── infrastructure/                # Infrastructure as Code
├── tests/                         # Test files
├── scripts/                       # Utility scripts
├── config/                        # Configuration files
├── .gitignore                     # Git ignore rules
├── LICENSE                        # Project license
└── README.md                      # This file
```

## 🚀 Development Phases

The project is structured in three main development phases:

### Phase 1: Core Outreach (MVP)
- Automated lead generation and import
- AI-powered lead scoring
- Email outreach with SendGrid
- Basic analytics dashboard
- User authentication and management

### Phase 2: Multi-Channel & Enrichment
- LinkedIn and SMS outreach
- Lead enrichment services integration
- Scheduling capabilities (Calendly/Google Calendar)
- Enhanced AI features
- Improved user experience

### Phase 3: Full AI Agents & Analytics
- Advanced multi-agent AI system
- Comprehensive analytics and forecasting
- CRM integrations (Salesforce, HubSpot, Zoho)
- Calendar synchronization
- Advanced AI model optimization

## 🛠️ Technology Stack

### Frontend
- **Framework**: React.js 18.x
- **Styling**: Tailwind CSS
- **State Management**: Redux/React Context API
- **HTTP Client**: Axios
- **Routing**: React Router

### Backend
- **Runtime**: Node.js 18.x / Python 3.10+
- **Serverless**: AWS Lambda
- **API**: AWS API Gateway
- **Database**: AWS DynamoDB
- **Queue**: AWS SQS
- **Authentication**: JWT

### AI & Communication
- **AI Platform**: Relevance AI, Google Gemini API
- **Email**: SendGrid
- **SMS**: Twilio
- **Social**: LinkedIn Marketing API

### DevOps & Infrastructure
- **Cloud**: AWS
- **Deployment**: Serverless Framework
- **CI/CD**: GitHub Actions
- **IaC**: AWS CloudFormation
- **Monitoring**: AWS CloudWatch

## 📋 Getting Started

### Prerequisites

- Node.js 18.x or later
- Python 3.10+ (for backend services)
- AWS CLI configured
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/GEMDevEng/AI_SDR.git
   cd AI_SDR
   ```

2. **Install dependencies**
   ```bash
   # Frontend
   cd src/frontend
   npm install

   # Backend
   cd ../backend
   npm install  # or pip install -r requirements.txt for Python
   ```

3. **Configure environment variables**
   ```bash
   cp config/.env.example .env
   # Edit .env with your API keys and configuration
   ```

4. **Deploy infrastructure**
   ```bash
   # Deploy AWS resources
   cd infrastructure
   serverless deploy
   ```

### Development

1. **Start frontend development server**
   ```bash
   cd src/frontend
   npm start
   ```

2. **Start backend services locally**
   ```bash
   cd src/backend
   serverless offline
   ```

## 📖 Documentation

Comprehensive documentation is organized in the `/docs` directory:

- **[Planning](/docs/planning/)**: Project planning, work breakdown, and team assignments
- **[Requirements](/docs/requirements/)**: Product requirements, specifications, and features
- **[Architecture](/docs/architecture/)**: Technical architecture, tech stack, and system design
- **[Strategy](/docs/strategy/)**: Strategic analysis and implementation strategies
- **[Implementation](/docs/implementation/)**: Detailed implementation guides and tutorials

### Key Documents

- [Product Requirements Document](/docs/requirements/product-requirements.md)
- [Technical Architecture](/docs/architecture/tech-stack.md)
- [Implementation Plan](/docs/planning/implementation-plan.md)
- [Work Breakdown Structure](/docs/planning/work-breakdown-structure.md)

## 🧪 Testing

The project includes comprehensive testing strategies:

- **Unit Tests**: Jest for component and function testing
- **Integration Tests**: API endpoint and service integration testing
- **End-to-End Tests**: Cypress for user flow validation
- **Manual Testing**: UI/UX validation on desktop and tablet devices

Run tests:
```bash
# Frontend tests
cd src/frontend
npm test

# Backend tests
cd src/backend
npm test

# E2E tests
npm run test:e2e
```

## 🚀 Deployment

The application uses a serverless architecture deployed on AWS:

1. **Automated Deployment**
   ```bash
   # Deploy via GitHub Actions (recommended)
   git push origin main
   ```

2. **Manual Deployment**
   ```bash
   # Deploy backend
   cd src/backend
   serverless deploy

   # Deploy frontend
   cd src/frontend
   npm run build
   # Deploy to Netlify or AWS S3
   ```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

Please read our [Contributing Guidelines](/docs/planning/coding-agent-rules.md) for details on our code of conduct and development process.

## 📊 Project Status

- **Current Phase**: Planning and Architecture
- **Next Milestone**: Phase 1 MVP Development
- **Target Market**: Mining equipment suppliers
- **Focus Regions**: Africa, North America

## 🔒 Security

- All communications use HTTPS encryption
- JWT-based authentication
- API keys stored in AWS Secrets Manager
- Input validation and sanitization
- Role-based access control

## 💰 Cost Optimization

The project prioritizes cost efficiency through:

- AWS Free Tier utilization
- Serverless pay-per-use model
- Free tiers of external services (SendGrid, Relevance AI)
- Open-source technology stack

## 📞 Support

For questions, issues, or contributions:

- **Issues**: [GitHub Issues](https://github.com/GEMDevEng/AI_SDR/issues)
- **Discussions**: [GitHub Discussions](https://github.com/GEMDevEng/AI_SDR/discussions)
- **Email**: <EMAIL>

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with modern web technologies and AI services
- Designed for the mining equipment industry
- Focused on automation and efficiency
- Scalable serverless architecture

---

**Note**: This project is currently in the planning and architecture phase. The source code structure is prepared for development based on the comprehensive documentation in the `/docs` directory.
