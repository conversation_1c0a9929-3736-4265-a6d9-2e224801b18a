import React from 'react';
import { useQuery } from 'react-query';
import { fetchDashboardData } from '../utils/api';

const Dashboard = () => {
  const { data, isLoading } = useQuery('dashboard', fetchDashboardData);

  if (isLoading) return <div>Loading...</div>;

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Dashboard</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-gray-500 text-sm font-medium">Total Leads</h3>
          <p className="text-3xl font-bold">{data?.totalLeads || 0}</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-gray-500 text-sm font-medium">Emails Sent</h3>
          <p className="text-3xl font-bold">{data?.emailsSent || 0}</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-gray-500 text-sm font-medium">Response Rate</h3>
          <p className="text-3xl font-bold">{data?.responseRate || 0}%</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-gray-500 text-sm font-medium">High Potential Leads</h3>
          <p className="text-3xl font-bold">{data?.highPotentialLeads || 0}</p>
        </div>
      </div>
      
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
        <div className="space-y-4">
          {data?.recentActivities?.map((activity: any) => (
            <div key={activity.id} className="border-b pb-2">
              <p className="text-sm">{activity.description}</p>
              <p className="text-xs text-gray-500">{activity.timestamp}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
