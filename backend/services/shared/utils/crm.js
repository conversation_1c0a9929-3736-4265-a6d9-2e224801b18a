const hubspot = require('@hubspot/api-client');
const Joi = require('joi');
const { logger, ValidationError, InternalServerError } = require('./api');
const { configService } = require('./config');
const { retryWithBackoff } = require('./helpers');

// CRM validation schemas
const connectCrmSchema = Joi.object({
  crmType: Joi.string().valid('hubspot', 'zoho', 'salesforce').required(),
  credentials: Joi.object().required()
});

const syncLeadsSchema = Joi.object({
  crmType: Joi.string().valid('hubspot', 'zoho', 'salesforce').required(),
  direction: Joi.string().valid('import', 'export', 'bidirectional').required(),
  leads: Joi.array().items(Joi.object()).optional(),
  filters: Joi.object().optional(),
  mapping: Joi.object().optional()
});

class CrmService {
  constructor() {
    this.connections = new Map();
    this.config = null;
  }

  /**
   * Initialize CRM service with configuration
   */
  async initialize() {
    if (this.config) {
      return;
    }

    try {
      this.config = await configService.getHubSpotConfig();
      logger.info('CRM service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize CRM service', { error: error.message });
      throw new InternalServerError('CRM service initialization failed');
    }
  }

  /**
   * Connect to CRM with validation and retry logic
   * @param {Object} connectionData - CRM connection data
   * @returns {Promise<Object>} Connection result
   */
  async connectCRM(connectionData) {
    await this.initialize();

    // Validate input
    const { error, value } = connectCrmSchema.validate(connectionData);
    if (error) {
      throw new ValidationError('Invalid CRM connection data', error.details);
    }

    const { crmType, credentials } = value;

    logger.info('Connecting to CRM', { crmType });

    try {
      let client;
      let connectionKey = `${crmType}-${JSON.stringify(credentials)}`;

      // Check if connection already exists
      if (this.connections.has(connectionKey)) {
        logger.info('Using existing CRM connection', { crmType });
        return { success: true, crmType, cached: true };
      }

      // Create new connection based on CRM type
      switch (crmType) {
        case 'hubspot':
          client = await this.connectHubSpot(credentials);
          break;
        case 'zoho':
          client = await this.connectZoho(credentials);
          break;
        case 'salesforce':
          client = await this.connectSalesforce(credentials);
          break;
        default:
          throw new ValidationError(`Unsupported CRM type: ${crmType}`);
      }

      // Store connection
      this.connections.set(connectionKey, {
        client,
        crmType,
        connectedAt: new Date().toISOString(),
        credentials: { ...credentials, password: '[REDACTED]' } // Don't store sensitive data
      });

      logger.info('CRM connection established successfully', { crmType });

      return {
        success: true,
        crmType,
        connectedAt: new Date().toISOString()
      };
    } catch (error) {
      logger.error('CRM connection failed', {
        crmType,
        error: error.message
      });

      if (error instanceof ValidationError) {
        throw error;
      }

      throw new InternalServerError(`Failed to connect to ${crmType}: ${error.message}`);
    }
  }

  /**
   * Connect to HubSpot
   */
  async connectHubSpot(credentials) {
    const accessToken = credentials.accessToken || this.config.accessToken;

    if (!accessToken) {
      throw new ValidationError('HubSpot access token is required');
    }

    const client = new hubspot.Client({ accessToken });

    // Test connection
    await retryWithBackoff(
      () => client.crm.contacts.basicApi.getPage(1),
      3,
      1000
    );

    return client;
  }

  /**
   * Connect to Zoho (placeholder - requires proper Zoho SDK)
   */
  async connectZoho(credentials) {
    // Note: zoho-crm package doesn't exist, this is a placeholder
    logger.warn('Zoho CRM integration not yet implemented');
    throw new ValidationError('Zoho CRM integration is not yet available');
  }

  /**
   * Connect to Salesforce (placeholder)
   */
  async connectSalesforce(credentials) {
    logger.warn('Salesforce CRM integration not yet implemented');
    throw new ValidationError('Salesforce CRM integration is not yet available');
  }

  /**
   * Sync leads with CRM
   * @param {Object} syncData - Sync configuration
   * @returns {Promise<Object>} Sync result
   */
  async syncLeads(syncData) {
    await this.initialize();

    // Validate input
    const { error, value } = syncLeadsSchema.validate(syncData);
    if (error) {
      throw new ValidationError('Invalid sync data', error.details);
    }

    const { crmType, direction, leads, filters, mapping } = value;

    logger.info('Starting CRM sync', { crmType, direction, leadsCount: leads?.length });

    try {
      let result;

      switch (crmType) {
        case 'hubspot':
          result = await this.syncHubSpotLeads(direction, leads, filters, mapping);
          break;
        case 'zoho':
          throw new ValidationError('Zoho sync not yet implemented');
        case 'salesforce':
          throw new ValidationError('Salesforce sync not yet implemented');
        default:
          throw new ValidationError(`Unsupported CRM type: ${crmType}`);
      }

      logger.info('CRM sync completed successfully', {
        crmType,
        direction,
        processed: result.processed,
        success: result.success,
        failed: result.failed
      });

      return result;
    } catch (error) {
      logger.error('CRM sync failed', {
        crmType,
        direction,
        error: error.message
      });

      if (error instanceof ValidationError) {
        throw error;
      }

      throw new InternalServerError(`CRM sync failed: ${error.message}`);
    }
  }

  /**
   * Sync leads with HubSpot
   */
  async syncHubSpotLeads(direction, leads = [], filters = {}, mapping = {}) {
    const connectionKey = Object.keys(Object.fromEntries(this.connections)).find(key =>
      this.connections.get(key).crmType === 'hubspot'
    );

    if (!connectionKey) {
      throw new ValidationError('No active HubSpot connection found');
    }

    const connection = this.connections.get(connectionKey);
    const client = connection.client;

    const result = {
      processed: 0,
      success: 0,
      failed: 0,
      errors: []
    };

    try {
      switch (direction) {
        case 'import':
          return await this.importFromHubSpot(client, filters, mapping, result);
        case 'export':
          return await this.exportToHubSpot(client, leads, mapping, result);
        case 'bidirectional':
          const importResult = await this.importFromHubSpot(client, filters, mapping, { ...result });
          const exportResult = await this.exportToHubSpot(client, leads, mapping, { ...result });

          return {
            processed: importResult.processed + exportResult.processed,
            success: importResult.success + exportResult.success,
            failed: importResult.failed + exportResult.failed,
            errors: [...importResult.errors, ...exportResult.errors],
            import: importResult,
            export: exportResult
          };
        default:
          throw new ValidationError(`Invalid sync direction: ${direction}`);
      }
    } catch (error) {
      logger.error('HubSpot sync operation failed', { direction, error: error.message });
      throw error;
    }
  }

  /**
   * Import leads from HubSpot
   */
  async importFromHubSpot(client, filters, mapping, result) {
    try {
      const properties = [
        'firstname',
        'lastname',
        'email',
        'company',
        'phone',
        'website',
        'industry',
        'annualrevenue',
        'city',
        'state',
        'country'
      ];

      let after = undefined;
      const importedLeads = [];

      do {
        const response = await client.crm.contacts.basicApi.getPage(
          100, // limit
          after,
          properties
        );

        for (const contact of response.results) {
          try {
            result.processed++;

            // Apply filters if provided
            if (filters && Object.keys(filters).length > 0) {
              const shouldInclude = Object.entries(filters).every(([key, value]) => {
                const contactValue = contact.properties[key];
                return contactValue && contactValue.includes(value);
              });

              if (!shouldInclude) {
                continue;
              }
            }

            // Map HubSpot contact to our lead format
            const lead = this.mapHubSpotContactToLead(contact, mapping);
            importedLeads.push(lead);
            result.success++;

          } catch (error) {
            result.failed++;
            result.errors.push({
              contactId: contact.id,
              error: error.message
            });
          }
        }

        after = response.paging?.next?.after;
      } while (after);

      return {
        ...result,
        leads: importedLeads
      };

    } catch (error) {
      logger.error('HubSpot import failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Export leads to HubSpot
   */
  async exportToHubSpot(client, leads, mapping, result) {
    try {
      const exportedContacts = [];

      for (const lead of leads) {
        try {
          result.processed++;

          // Map our lead to HubSpot contact format
          const contactData = this.mapLeadToHubSpotContact(lead, mapping);

          // Create or update contact in HubSpot
          const response = await client.crm.contacts.basicApi.create({
            properties: contactData
          });

          exportedContacts.push({
            leadId: lead.leadId,
            hubspotId: response.id
          });
          result.success++;

        } catch (error) {
          result.failed++;
          result.errors.push({
            leadId: lead.leadId,
            error: error.message
          });
        }
      }

      return {
        ...result,
        contacts: exportedContacts
      };

    } catch (error) {
      logger.error('HubSpot export failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Map HubSpot contact to our lead format
   */
  mapHubSpotContactToLead(contact, mapping = {}) {
    const props = contact.properties;

    return {
      companyName: props.company || '',
      contactName: `${props.firstname || ''} ${props.lastname || ''}`.trim(),
      contactEmail: props.email || '',
      phone: props.phone || '',
      revenue: props.annualrevenue ? parseFloat(props.annualrevenue) : null,
      industry: props.industry || '',
      location: [props.city, props.state, props.country].filter(Boolean).join(', '),
      source: 'hubspot',
      ...mapping // Apply custom mapping overrides
    };
  }

  /**
   * Map our lead to HubSpot contact format
   */
  mapLeadToHubSpotContact(lead, mapping = {}) {
    const nameParts = (lead.contactName || '').split(' ');

    const contactData = {
      email: lead.contactEmail,
      company: lead.companyName,
      phone: lead.phone,
      industry: lead.industry,
      annualrevenue: lead.revenue?.toString(),
      ...mapping // Apply custom mapping overrides
    };

    if (nameParts.length > 0) {
      contactData.firstname = nameParts[0];
      if (nameParts.length > 1) {
        contactData.lastname = nameParts.slice(1).join(' ');
      }
    }

    // Parse location if available
    if (lead.location) {
      const locationParts = lead.location.split(',').map(part => part.trim());
      if (locationParts.length >= 1) contactData.city = locationParts[0];
      if (locationParts.length >= 2) contactData.state = locationParts[1];
      if (locationParts.length >= 3) contactData.country = locationParts[2];
    }

    return contactData;
  }

  /**
   * Get CRM connection status
   */
  getConnectionStatus(crmType) {
    const connections = Array.from(this.connections.values())
      .filter(conn => conn.crmType === crmType);

    return {
      connected: connections.length > 0,
      count: connections.length,
      connections: connections.map(conn => ({
        crmType: conn.crmType,
        connectedAt: conn.connectedAt
      }))
    };
  }

  /**
   * Disconnect from CRM
   */
  disconnect(crmType) {
    const keysToRemove = [];

    for (const [key, connection] of this.connections) {
      if (connection.crmType === crmType) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach(key => this.connections.delete(key));

    logger.info('Disconnected from CRM', { crmType, removedConnections: keysToRemove.length });

    return { success: true, disconnected: keysToRemove.length };
  }
}

// Export singleton instance
const crmService = new CrmService();

module.exports = {
  CrmService,
  crmService,
  // Legacy exports for backward compatibility
  connectCRM: (crmType, credentials) => crmService.connectCRM({ crmType, credentials }),
  syncLeads: (crmType, direction) => crmService.syncLeads({ crmType, direction })
};