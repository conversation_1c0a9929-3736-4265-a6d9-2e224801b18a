/**
 * Simple Railway-optimized server for AI SDR backend
 * Minimal dependencies, maximum reliability
 */

const express = require('express');
const cors = require('cors');

const app = express();
const port = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Startup logging
console.log('🚀 AI SDR Backend starting...');
console.log('📍 Port:', port);
console.log('🌍 Environment:', process.env.NODE_ENV || 'development');
console.log('🔑 Gemini API Key:', process.env.GEMINI_API_KEY ? 'Set ✅' : 'Missing ❌');

// Simple health check endpoint
app.get('/health', (req, res) => {
  console.log('🏥 Health check requested');
  const healthData = {
    status: 'healthy',
    service: 'AI SDR Backend',
    timestamp: new Date().toISOString(),
    port: port,
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    geminiConfigured: !!process.env.GEMINI_API_KEY
  };
  console.log('✅ Health check response:', healthData);
  res.json(healthData);
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'AI SDR Backend is running!',
    service: 'AI Sales Development Representative',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      docs: '/api/docs'
    }
  });
});

// API documentation endpoint
app.get('/api/docs', (req, res) => {
  res.json({
    title: 'AI SDR Backend API',
    version: '1.0.0',
    description: 'AI-powered Sales Development Representative backend',
    status: 'operational',
    geminiIntegration: !!process.env.GEMINI_API_KEY,
    endpoints: {
      'GET /': 'Service information',
      'GET /health': 'Health check',
      'GET /api/docs': 'API documentation'
    }
  });
});

// Simple AI endpoint (placeholder for now)
app.post('/api/generate-email', (req, res) => {
  console.log('📧 Email generation requested');
  
  if (!process.env.GEMINI_API_KEY) {
    return res.status(503).json({
      error: 'AI service not configured',
      message: 'Gemini API key is missing'
    });
  }

  // Placeholder response
  res.json({
    success: true,
    message: 'AI email generation endpoint is ready',
    timestamp: new Date().toISOString(),
    note: 'Full AI integration will be enabled once deployed'
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('❌ Server error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: err.message,
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not found',
    message: `Route ${req.originalUrl} not found`,
    availableEndpoints: ['/', '/health', '/api/docs']
  });
});

// Start server
const server = app.listen(port, '0.0.0.0', () => {
  console.log(`🚀 AI SDR Backend running on port ${port}`);
  console.log(`🌍 Server listening on 0.0.0.0:${port}`);
  console.log(`❤️  Health Check: http://localhost:${port}/health`);
  console.log(`📚 API Documentation: http://localhost:${port}/api/docs`);
  console.log(`✅ Server started successfully!`);
});

// Error handling
server.on('error', (error) => {
  console.error('❌ Server error:', error);
  if (error.code === 'EADDRINUSE') {
    console.error(`❌ Port ${port} is already in use`);
  }
  process.exit(1);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

module.exports = app;
