const winston = require('winston');
const Joi = require('joi');

// Configure logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'ai-sdr-backend' },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

// Custom error classes
class ValidationError extends Error {
  constructor(message, details = null) {
    super(message);
    this.name = 'ValidationError';
    this.statusCode = 400;
    this.details = details;
  }
}

class NotFoundError extends Error {
  constructor(message = 'Resource not found') {
    super(message);
    this.name = 'NotFoundError';
    this.statusCode = 404;
  }
}

class UnauthorizedError extends Error {
  constructor(message = 'Unauthorized') {
    super(message);
    this.name = 'UnauthorizedError';
    this.statusCode = 401;
  }
}

class ForbiddenError extends Error {
  constructor(message = 'Forbidden') {
    super(message);
    this.name = 'ForbiddenError';
    this.statusCode = 403;
  }
}

class InternalServerError extends Error {
  constructor(message = 'Internal server error') {
    super(message);
    this.name = 'InternalServerError';
    this.statusCode = 500;
  }
}

// Response helper functions
const createResponse = (statusCode, body, headers = {}) => {
  const defaultHeaders = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
    'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
  };

  return {
    statusCode,
    headers: { ...defaultHeaders, ...headers },
    body: JSON.stringify(body)
  };
};

const successResponse = (data, statusCode = 200) => {
  return createResponse(statusCode, {
    success: true,
    data
  });
};

const errorResponse = (error, statusCode = 500) => {
  const errorBody = {
    success: false,
    error: {
      message: error.message || 'Internal server error',
      type: error.name || 'Error'
    }
  };

  if (error.details) {
    errorBody.error.details = error.details;
  }

  return createResponse(statusCode, errorBody);
};

// Validation helper
const validateInput = (data, schema) => {
  const { error, value } = schema.validate(data, { abortEarly: false });
  if (error) {
    const details = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message
    }));
    throw new ValidationError('Validation failed', details);
  }
  return value;
};

// Lambda wrapper for error handling
const lambdaWrapper = (handler) => {
  return async (event, context) => {
    try {
      logger.info('Lambda invocation started', {
        requestId: context.awsRequestId,
        functionName: context.functionName,
        httpMethod: event.httpMethod,
        path: event.path
      });

      const result = await handler(event, context);
      
      logger.info('Lambda invocation completed successfully', {
        requestId: context.awsRequestId,
        statusCode: result.statusCode
      });

      return result;
    } catch (error) {
      logger.error('Lambda invocation failed', {
        requestId: context.awsRequestId,
        error: error.message,
        stack: error.stack
      });

      if (error instanceof ValidationError) {
        return errorResponse(error, error.statusCode);
      }
      if (error instanceof NotFoundError) {
        return errorResponse(error, error.statusCode);
      }
      if (error instanceof UnauthorizedError) {
        return errorResponse(error, error.statusCode);
      }
      if (error instanceof ForbiddenError) {
        return errorResponse(error, error.statusCode);
      }

      // For unknown errors, don't expose internal details
      return errorResponse(new InternalServerError(), 500);
    }
  };
};

// Parse JSON body safely
const parseBody = (event) => {
  if (!event.body) {
    return {};
  }
  
  try {
    return JSON.parse(event.body);
  } catch (error) {
    throw new ValidationError('Invalid JSON in request body');
  }
};

// Extract path parameters
const getPathParameters = (event) => {
  return event.pathParameters || {};
};

// Extract query parameters
const getQueryParameters = (event) => {
  return event.queryStringParameters || {};
};

module.exports = {
  logger,
  ValidationError,
  NotFoundError,
  UnauthorizedError,
  ForbiddenError,
  InternalServerError,
  createResponse,
  successResponse,
  errorResponse,
  validateInput,
  lambdaWrapper,
  parseBody,
  getPathParameters,
  getQueryParameters
};
