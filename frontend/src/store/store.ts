import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice.ts';
import leadsSlice from './slices/leadsSlice.ts';
import dashboardSlice from './slices/dashboardSlice.ts';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    leads: leadsSlice,
    dashboard: dashboardSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
