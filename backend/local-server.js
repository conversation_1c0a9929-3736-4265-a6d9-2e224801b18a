const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    service: 'AI SDR Backend'
  });
});

// Simple test endpoints for API verification
app.get('/auth/test', (req, res) => {
  res.json({ service: 'auth', status: 'available', endpoints: ['login', 'register', 'refresh', 'verify'] });
});

app.get('/leads/test', (req, res) => {
  res.json({ service: 'leads', status: 'available', endpoints: ['list', 'create', 'get', 'update', 'delete', 'import'] });
});

app.get('/outreach/test', (req, res) => {
  res.json({ service: 'outreach', status: 'available', endpoints: ['campaign', 'email', 'sms', 'status'] });
});

app.get('/analytics/test', (req, res) => {
  res.json({ service: 'analytics', status: 'available', endpoints: ['dashboard', 'metrics', 'report'] });
});

app.get('/integrations/test', (req, res) => {
  res.json({ service: 'integrations', status: 'available', endpoints: ['connect', 'sync', 'status'] });
});

// API documentation endpoint
app.get('/api/docs', (req, res) => {
  res.json({
    title: 'AI SDR Backend API',
    version: '1.0.0',
    endpoints: {
      auth: {
        'POST /auth/login': 'User login',
        'POST /auth/register': 'User registration',
        'POST /auth/refresh': 'Token refresh',
        'GET /auth/verify': 'Token verification'
      },
      leads: {
        'GET /leads': 'List leads',
        'POST /leads': 'Create lead',
        'GET /leads/:id': 'Get lead by ID',
        'PUT /leads/:id': 'Update lead',
        'DELETE /leads/:id': 'Delete lead',
        'POST /leads/import': 'Bulk import leads'
      },
      outreach: {
        'POST /outreach/campaign': 'Execute campaign',
        'POST /outreach/email': 'Send email',
        'POST /outreach/sms': 'Send SMS',
        'GET /outreach/status/:campaignId': 'Campaign status'
      },
      analytics: {
        'GET /analytics/dashboard': 'Dashboard metrics',
        'GET /analytics/metrics': 'Custom metrics',
        'POST /analytics/report': 'Generate report'
      },
      integrations: {
        'POST /integrations/connect': 'Connect CRM',
        'POST /integrations/sync': 'Sync with CRM',
        'GET /integrations/status/:crmType': 'Connection status'
      }
    }
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: error.message
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not found',
    message: `Route ${req.method} ${req.originalUrl} not found`
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 AI SDR Backend running on http://localhost:${PORT}`);
  console.log(`📚 API Documentation: http://localhost:${PORT}/api/docs`);
  console.log(`❤️  Health Check: http://localhost:${PORT}/health`);
});

module.exports = app;
