const AWS = require('aws-sdk');
const { logger, NotFoundError, InternalServerError } = require('./api');

// Configure DynamoDB
const dynamodb = new AWS.DynamoDB.DocumentClient({
  region: process.env.AWS_REGION || 'us-east-1'
});

/**
 * Generic DynamoDB operations utility
 */
class DatabaseService {
  constructor(tableName) {
    this.tableName = tableName;
  }

  /**
   * Create a new item in the table
   * @param {Object} item - The item to create
   * @returns {Promise<Object>} The created item
   */
  async create(item) {
    try {
      const params = {
        TableName: this.tableName,
        Item: {
          ...item,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        ConditionExpression: 'attribute_not_exists(#pk)',
        ExpressionAttributeNames: {
          '#pk': this.getPrimaryKey()
        }
      };

      await dynamodb.put(params).promise();
      
      logger.info('Item created successfully', {
        tableName: this.tableName,
        itemId: item[this.getPrimaryKey()]
      });

      return params.Item;
    } catch (error) {
      if (error.code === 'ConditionalCheckFailedException') {
        throw new Error(`Item with ${this.getPrimaryKey()} already exists`);
      }
      logger.error('Failed to create item', {
        tableName: this.tableName,
        error: error.message
      });
      throw new InternalServerError('Failed to create item');
    }
  }

  /**
   * Get an item by its primary key
   * @param {string} id - The primary key value
   * @returns {Promise<Object>} The item
   */
  async get(id) {
    try {
      const params = {
        TableName: this.tableName,
        Key: {
          [this.getPrimaryKey()]: id
        }
      };

      const result = await dynamodb.get(params).promise();
      
      if (!result.Item) {
        throw new NotFoundError(`Item with ${this.getPrimaryKey()} ${id} not found`);
      }

      logger.info('Item retrieved successfully', {
        tableName: this.tableName,
        itemId: id
      });

      return result.Item;
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      logger.error('Failed to get item', {
        tableName: this.tableName,
        itemId: id,
        error: error.message
      });
      throw new InternalServerError('Failed to retrieve item');
    }
  }

  /**
   * Update an item
   * @param {string} id - The primary key value
   * @param {Object} updates - The fields to update
   * @returns {Promise<Object>} The updated item
   */
  async update(id, updates) {
    try {
      const updateExpression = [];
      const expressionAttributeNames = {};
      const expressionAttributeValues = {};

      // Build update expression
      Object.keys(updates).forEach((key, index) => {
        const attrName = `#attr${index}`;
        const attrValue = `:val${index}`;
        
        updateExpression.push(`${attrName} = ${attrValue}`);
        expressionAttributeNames[attrName] = key;
        expressionAttributeValues[attrValue] = updates[key];
      });

      // Add updatedAt timestamp
      updateExpression.push('#updatedAt = :updatedAt');
      expressionAttributeNames['#updatedAt'] = 'updatedAt';
      expressionAttributeValues[':updatedAt'] = new Date().toISOString();

      const params = {
        TableName: this.tableName,
        Key: {
          [this.getPrimaryKey()]: id
        },
        UpdateExpression: `SET ${updateExpression.join(', ')}`,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: expressionAttributeValues,
        ReturnValues: 'ALL_NEW',
        ConditionExpression: `attribute_exists(${this.getPrimaryKey()})`
      };

      const result = await dynamodb.update(params).promise();
      
      logger.info('Item updated successfully', {
        tableName: this.tableName,
        itemId: id
      });

      return result.Attributes;
    } catch (error) {
      if (error.code === 'ConditionalCheckFailedException') {
        throw new NotFoundError(`Item with ${this.getPrimaryKey()} ${id} not found`);
      }
      logger.error('Failed to update item', {
        tableName: this.tableName,
        itemId: id,
        error: error.message
      });
      throw new InternalServerError('Failed to update item');
    }
  }

  /**
   * Delete an item
   * @param {string} id - The primary key value
   * @returns {Promise<void>}
   */
  async delete(id) {
    try {
      const params = {
        TableName: this.tableName,
        Key: {
          [this.getPrimaryKey()]: id
        },
        ConditionExpression: `attribute_exists(${this.getPrimaryKey()})`
      };

      await dynamodb.delete(params).promise();
      
      logger.info('Item deleted successfully', {
        tableName: this.tableName,
        itemId: id
      });
    } catch (error) {
      if (error.code === 'ConditionalCheckFailedException') {
        throw new NotFoundError(`Item with ${this.getPrimaryKey()} ${id} not found`);
      }
      logger.error('Failed to delete item', {
        tableName: this.tableName,
        itemId: id,
        error: error.message
      });
      throw new InternalServerError('Failed to delete item');
    }
  }

  /**
   * Scan table with optional filters
   * @param {Object} options - Scan options
   * @returns {Promise<Array>} Array of items
   */
  async scan(options = {}) {
    try {
      const params = {
        TableName: this.tableName,
        ...options
      };

      const result = await dynamodb.scan(params).promise();
      
      logger.info('Table scanned successfully', {
        tableName: this.tableName,
        itemCount: result.Items.length
      });

      return result.Items;
    } catch (error) {
      logger.error('Failed to scan table', {
        tableName: this.tableName,
        error: error.message
      });
      throw new InternalServerError('Failed to scan table');
    }
  }

  /**
   * Query table with conditions
   * @param {Object} keyCondition - Key condition expression
   * @param {Object} options - Additional query options
   * @returns {Promise<Array>} Array of items
   */
  async query(keyCondition, options = {}) {
    try {
      const params = {
        TableName: this.tableName,
        KeyConditionExpression: keyCondition.expression,
        ExpressionAttributeNames: keyCondition.names || {},
        ExpressionAttributeValues: keyCondition.values || {},
        ...options
      };

      const result = await dynamodb.query(params).promise();
      
      logger.info('Table queried successfully', {
        tableName: this.tableName,
        itemCount: result.Items.length
      });

      return result.Items;
    } catch (error) {
      logger.error('Failed to query table', {
        tableName: this.tableName,
        error: error.message
      });
      throw new InternalServerError('Failed to query table');
    }
  }

  /**
   * Get the primary key field name for this table
   * Override in subclasses
   * @returns {string} Primary key field name
   */
  getPrimaryKey() {
    throw new Error('getPrimaryKey() must be implemented by subclass');
  }
}

module.exports = {
  DatabaseService,
  dynamodb
};
