# Development Environment Configuration
# AI SDR Backend - Development Stage

# Service Configuration
service:
  name: ai-sdr-backend-dev
  stage: dev
  region: us-east-1

# Lambda Configuration
lambda:
  timeout: 30
  memorySize: 512
  runtime: nodejs18.x
  environment:
    NODE_ENV: development
    LOG_LEVEL: debug
    CORS_ORIGIN: "http://localhost:3000,http://localhost:3001"

# DynamoDB Configuration
dynamodb:
  tables:
    leads:
      tableName: ai-sdr-leads-dev
      billingMode: PAY_PER_REQUEST
      pointInTimeRecovery: false
    campaigns:
      tableName: ai-sdr-campaigns-dev
      billingMode: PAY_PER_REQUEST
      pointInTimeRecovery: false
    users:
      tableName: ai-sdr-users-dev
      billingMode: PAY_PER_REQUEST
      pointInTimeRecovery: false

# API Gateway Configuration
apiGateway:
  restApiId: ${env:API_GATEWAY_REST_API_ID, ''}
  restApiRootResourceId: ${env:API_GATEWAY_ROOT_RESOURCE_ID, ''}
  minimumCompressionSize: 1024
  binaryMediaTypes:
    - '*/*'

# External Services Configuration
external:
  sendgrid:
    apiKey: ${env:SENDGRID_API_KEY}
    fromEmail: ${env:SENDGRID_FROM_EMAIL, '<EMAIL>'}
    fromName: ${env:SENDGRID_FROM_NAME, 'AI SDR Development'}
  
  twilio:
    accountSid: ${env:TWILIO_ACCOUNT_SID}
    authToken: ${env:TWILIO_AUTH_TOKEN}
    fromNumber: ${env:TWILIO_FROM_NUMBER}
  
  hubspot:
    apiKey: ${env:HUBSPOT_API_KEY}
    portalId: ${env:HUBSPOT_PORTAL_ID}
  
  gemini:
    apiKey: ${env:GEMINI_API_KEY}
    model: ${env:GEMINI_MODEL, 'gemini-pro'}
    maxTokens: ${env:GEMINI_MAX_TOKENS, '500'}
  
  hunter:
    apiKey: ${env:HUNTER_API_KEY}

# Security Configuration
security:
  jwt:
    secret: ${env:JWT_SECRET}
    expiresIn: ${env:JWT_EXPIRES_IN, '24h'}
  
  cors:
    origin: 
      - http://localhost:3000
      - http://localhost:3001
    credentials: true
    methods:
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
    headers:
      - Content-Type
      - Authorization
      - X-Requested-With

# Monitoring Configuration
monitoring:
  cloudwatch:
    enabled: true
    logRetentionInDays: 7
  
  xray:
    enabled: false
  
  alarms:
    enabled: false

# Custom Configuration
custom:
  webpack:
    webpackConfig: 'webpack.config.js'
    includeModules: true
    packager: 'npm'
  
  serverless-offline:
    httpPort: 3001
    lambdaPort: 3002
    host: localhost
    stage: dev
    region: us-east-1
    printOutput: true

# Plugins
plugins:
  - serverless-offline
  - serverless-dotenv-plugin

# Package Configuration
package:
  individually: true
  exclude:
    - .git/**
    - .gitignore
    - README.md
    - .env*
    - tests/**
    - coverage/**
    - docs/**
    - '*.md'
