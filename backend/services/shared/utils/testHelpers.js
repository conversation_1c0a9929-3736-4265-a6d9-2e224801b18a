const AWS = require('aws-sdk');
const { v4: uuidv4 } = require('uuid');

// Mock AWS services for testing
const mockDynamoDB = {
  put: jest.fn().mockReturnValue({ promise: jest.fn().mockResolvedValue({}) }),
  get: jest.fn().mockReturnValue({ promise: jest.fn().mockResolvedValue({ Item: null }) }),
  update: jest.fn().mockReturnValue({ promise: jest.fn().mockResolvedValue({ Attributes: {} }) }),
  delete: jest.fn().mockReturnValue({ promise: jest.fn().mockResolvedValue({}) }),
  scan: jest.fn().mockReturnValue({ promise: jest.fn().mockResolvedValue({ Items: [] }) }),
  query: jest.fn().mockReturnValue({ promise: jest.fn().mockResolvedValue({ Items: [] }) })
};

const mockSecretsManager = {
  getSecretValue: jest.fn().mockReturnValue({ 
    promise: jest.fn().mockResolvedValue({ 
      SecretString: JSON.stringify({ testSecret: 'testValue' }) 
    }) 
  })
};

const mockSSM = {
  getParameter: jest.fn().mockReturnValue({ 
    promise: jest.fn().mockResolvedValue({ 
      Parameter: { Value: 'testParameterValue' } 
    }) 
  })
};

const mockCloudWatch = {
  getMetricData: jest.fn().mockReturnValue({ 
    promise: jest.fn().mockResolvedValue({ 
      MetricDataResults: [
        { Id: 'emailsSent', Values: [10, 20, 30] },
        { Id: 'emailsOpened', Values: [5, 10, 15] }
      ] 
    }) 
  }),
  putMetricData: jest.fn().mockReturnValue({ promise: jest.fn().mockResolvedValue({}) })
};

const mockSendGrid = {
  send: jest.fn().mockResolvedValue([{ headers: { 'x-message-id': 'test-message-id' } }])
};

const mockTwilio = {
  messages: {
    create: jest.fn().mockResolvedValue({
      sid: 'test-message-sid',
      status: 'sent',
      from: '+1234567890',
      to: '+0987654321',
      dateCreated: new Date(),
      price: '0.0075',
      priceUnit: 'USD'
    })
  }
};

const mockHubSpot = {
  crm: {
    contacts: {
      basicApi: {
        getPage: jest.fn().mockResolvedValue({
          results: [
            {
              id: '123',
              properties: {
                firstname: 'John',
                lastname: 'Doe',
                email: '<EMAIL>',
                company: 'Test Company'
              }
            }
          ],
          paging: null
        }),
        create: jest.fn().mockResolvedValue({ id: '456' })
      }
    }
  }
};

/**
 * Test helper utilities
 */
class TestHelpers {
  /**
   * Setup AWS mocks
   */
  static setupAWSMocks() {
    // Mock DynamoDB
    AWS.DynamoDB.DocumentClient = jest.fn(() => mockDynamoDB);
    
    // Mock Secrets Manager
    AWS.SecretsManager = jest.fn(() => mockSecretsManager);
    
    // Mock SSM
    AWS.SSM = jest.fn(() => mockSSM);
    
    // Mock CloudWatch
    AWS.CloudWatch = jest.fn(() => mockCloudWatch);
  }

  /**
   * Setup external service mocks
   */
  static setupExternalMocks() {
    // Mock SendGrid
    jest.doMock('@sendgrid/mail', () => ({
      setApiKey: jest.fn(),
      send: mockSendGrid.send
    }));

    // Mock Twilio
    jest.doMock('twilio', () => jest.fn(() => mockTwilio));

    // Mock HubSpot
    jest.doMock('@hubspot/api-client', () => ({
      Client: jest.fn(() => mockHubSpot)
    }));
  }

  /**
   * Generate test data
   */
  static generateTestLead(overrides = {}) {
    return {
      leadId: uuidv4(),
      companyName: 'Test Company',
      contactEmail: '<EMAIL>',
      contactName: 'Test Contact',
      phone: '+1234567890',
      revenue: 1000000,
      industry: 'Technology',
      location: 'San Francisco, CA, USA',
      expansionPlans: 'Looking to expand operations',
      score: 75,
      outreachMessage: 'Test outreach message',
      emailStatus: 'pending',
      tags: ['test', 'demo'],
      notes: 'Test notes',
      source: 'test',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...overrides
    };
  }

  static generateTestCampaign(overrides = {}) {
    return {
      campaignId: uuidv4(),
      name: 'Test Campaign',
      description: 'Test campaign description',
      status: 'draft',
      channels: ['email'],
      schedule: 'immediate',
      leads: [],
      template: {
        subject: 'Test Subject',
        body: 'Test email body',
        variables: {}
      },
      settings: {
        delayBetweenSends: 1000,
        maxSendsPerDay: 100,
        trackOpens: true,
        trackClicks: true
      },
      tags: ['test'],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...overrides
    };
  }

  static generateTestUser(overrides = {}) {
    return {
      userId: uuidv4(),
      username: 'testuser',
      email: '<EMAIL>',
      passwordHash: '$2b$12$hashedpassword',
      role: 'user',
      status: 'active',
      profile: {
        firstName: 'Test',
        lastName: 'User',
        company: 'Test Company',
        phone: '+1234567890'
      },
      preferences: {
        notifications: true,
        theme: 'light',
        timezone: 'UTC'
      },
      lastLoginAt: new Date().toISOString(),
      loginCount: 5,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...overrides
    };
  }

  /**
   * Generate test Lambda event
   */
  static generateLambdaEvent(overrides = {}) {
    return {
      httpMethod: 'GET',
      path: '/test',
      pathParameters: null,
      queryStringParameters: null,
      headers: {
        'Content-Type': 'application/json'
      },
      body: null,
      requestContext: {
        requestId: uuidv4(),
        stage: 'test'
      },
      ...overrides
    };
  }

  /**
   * Generate test Lambda context
   */
  static generateLambdaContext(overrides = {}) {
    return {
      awsRequestId: uuidv4(),
      functionName: 'test-function',
      functionVersion: '1',
      invokedFunctionArn: 'arn:aws:lambda:us-east-1:123456789012:function:test-function',
      memoryLimitInMB: '128',
      remainingTimeInMillis: 30000,
      logGroupName: '/aws/lambda/test-function',
      logStreamName: '2023/01/01/[$LATEST]test',
      getRemainingTimeInMillis: () => 30000,
      done: jest.fn(),
      fail: jest.fn(),
      succeed: jest.fn(),
      ...overrides
    };
  }

  /**
   * Setup test environment
   */
  static setupTestEnvironment() {
    // Set test environment variables
    process.env.NODE_ENV = 'test';
    process.env.AWS_REGION = 'us-east-1';
    process.env.STAGE = 'test';
    process.env.DYNAMODB_TABLE_PREFIX = 'ai-sdr-test';
    process.env.USERS_TABLE = 'ai-sdr-test-users';
    process.env.LEADS_TABLE = 'ai-sdr-test-leads';
    process.env.CAMPAIGNS_TABLE = 'ai-sdr-test-campaigns';
    process.env.JOBS_TABLE = 'ai-sdr-test-jobs';
    process.env.JWT_SECRET = 'test-jwt-secret';
    process.env.LOG_LEVEL = 'error'; // Reduce log noise in tests

    // Setup mocks
    this.setupAWSMocks();
    this.setupExternalMocks();
    this.setupLoggerMock();
  }

  /**
   * Setup logger mock
   */
  static setupLoggerMock() {
    // Mock winston logger
    const mockLogger = {
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      debug: jest.fn()
    };

    jest.doMock('winston', () => ({
      createLogger: jest.fn(() => mockLogger),
      format: {
        combine: jest.fn(),
        timestamp: jest.fn(),
        errors: jest.fn(),
        json: jest.fn(),
        colorize: jest.fn(),
        simple: jest.fn()
      },
      transports: {
        Console: jest.fn()
      }
    }));

    return mockLogger;
  }

  /**
   * Clean up test environment
   */
  static cleanupTestEnvironment() {
    // Clear all mocks
    jest.clearAllMocks();
    jest.resetModules();
    
    // Reset environment variables
    delete process.env.NODE_ENV;
    delete process.env.AWS_REGION;
    delete process.env.STAGE;
    delete process.env.DYNAMODB_TABLE_PREFIX;
    delete process.env.USERS_TABLE;
    delete process.env.LEADS_TABLE;
    delete process.env.CAMPAIGNS_TABLE;
    delete process.env.JOBS_TABLE;
    delete process.env.JWT_SECRET;
    delete process.env.LOG_LEVEL;
  }

  /**
   * Assert response format
   */
  static assertValidResponse(response, expectedStatusCode = 200) {
    expect(response).toHaveProperty('statusCode', expectedStatusCode);
    expect(response).toHaveProperty('headers');
    expect(response).toHaveProperty('body');
    expect(response.headers).toHaveProperty('Content-Type', 'application/json');
    
    const body = JSON.parse(response.body);
    expect(body).toHaveProperty('success');
    
    if (expectedStatusCode >= 200 && expectedStatusCode < 300) {
      expect(body.success).toBe(true);
      expect(body).toHaveProperty('data');
    } else {
      expect(body.success).toBe(false);
      expect(body).toHaveProperty('error');
    }
    
    return body;
  }

  /**
   * Wait for async operations
   */
  static async waitFor(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get mock instances for assertions
   */
  static getMocks() {
    return {
      dynamodb: mockDynamoDB,
      secretsManager: mockSecretsManager,
      ssm: mockSSM,
      cloudwatch: mockCloudWatch,
      sendgrid: mockSendGrid,
      twilio: mockTwilio,
      hubspot: mockHubSpot
    };
  }
}

module.exports = TestHelpers;
