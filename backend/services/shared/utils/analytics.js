const AWS = require('aws-sdk');
const cloudwatch = new AWS.CloudWatch();

module.exports.getCampaignMetrics = async () => {
  try {
    const params = {
      MetricDataQueries: [
        {
          Id: 'campaignMetrics',
          MetricStat: {
            Metric: {
              Namespace: 'AI_SDR',
              MetricName: 'CampaignPerformance'
            },
            Period: 86400, // 1 day
            Stat: 'Sum'
          }
        }
      ],
      StartTime: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
      EndTime: new Date()
    };

    const data = await cloudwatch.getMetricData(params).promise();
    return {
      sent: data.MetricDataResults[0].Values.reduce((a, b) => a + b, 0),
      opened: data.MetricDataResults[1]?.Values.reduce((a, b) => a + b, 0) || 0,
      replied: data.MetricDataResults[2]?.Values.reduce((a, b) => a + b, 0) || 0
    };
  } catch (error) {
    console.error('Analytics error:', error);
    throw error;
  }
};