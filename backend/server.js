/**
 * Simple Railway-optimized server for AI SDR backend
 * Minimal dependencies, maximum reliability
 */

const express = require('express');
const cors = require('cors');

const app = express();
const port = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Startup logging
console.log('🚀 AI SDR Backend starting...');
console.log('📍 Port:', port);
console.log('🌍 Environment:', process.env.NODE_ENV || 'development');
console.log('🔑 Gemini API Key:', process.env.GEMINI_API_KEY ? 'Set ✅' : 'Missing ❌');

// Simple health check endpoint
app.get('/health', (req, res) => {
  console.log('🏥 Health check requested');
  const healthData = {
    status: 'healthy',
    service: 'AI SDR Backend',
    timestamp: new Date().toISOString(),
    port: port,
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    geminiConfigured: !!process.env.GEMINI_API_KEY
  };
  console.log('✅ Health check response:', healthData);
  res.json(healthData);
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'AI SDR Backend is running!',
    service: 'AI Sales Development Representative',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      docs: '/api/docs'
    }
  });
});

// API documentation endpoint
app.get('/api/docs', (req, res) => {
  res.json({
    title: 'AI SDR Backend API',
    version: '1.0.0',
    description: 'AI-powered Sales Development Representative backend with Google Gemini integration',
    status: 'operational',
    geminiIntegration: !!process.env.GEMINI_API_KEY,
    aiProvider: 'Google Gemini',
    endpoints: {
      'GET /': 'Service information',
      'GET /health': 'Health check and system status',
      'GET /api/docs': 'API documentation',
      'POST /api/generate-email': 'Generate AI-powered personalized emails',
      'POST /api/score-lead': 'Calculate AI-powered lead scores and analysis',
      'POST /api/generate-strategy': 'Generate comprehensive campaign strategies'
    },
    examples: {
      generateEmail: {
        url: 'POST /api/generate-email',
        body: {
          lead: { name: 'John Doe', company: 'Acme Corp', industry: 'Manufacturing' },
          campaign: { name: 'Q1 Outreach', goal: 'Generate meetings' }
        }
      },
      scoreLead: {
        url: 'POST /api/score-lead',
        body: {
          lead: { name: 'Jane Smith', company: 'Tech Solutions', role: 'CTO', budget: '$50k' }
        }
      },
      generateStrategy: {
        url: 'POST /api/generate-strategy',
        body: {
          leads: [{ name: 'Lead 1', company: 'Company A' }],
          campaignGoals: { objective: 'Increase sales', target: '20% growth' }
        }
      }
    }
  });
});

// AI-powered email generation endpoint
app.post('/api/generate-email', async (req, res) => {
  console.log('📧 Email generation requested');

  if (!process.env.GEMINI_API_KEY) {
    return res.status(503).json({
      error: 'AI service not configured',
      message: 'Gemini API key is missing'
    });
  }

  try {
    const { lead, campaign } = req.body;

    if (!lead || !campaign) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'Both lead and campaign data are required'
      });
    }

    // Generate AI-powered email using Google Gemini
    const prompt = `Generate a personalized sales email for:
    Lead: ${lead.name || 'Prospect'} at ${lead.company || 'their company'}
    Industry: ${lead.industry || 'general business'}
    Campaign: ${campaign.name || 'outreach campaign'}
    Goal: ${campaign.goal || 'generate interest'}

    Create a professional, engaging email that:
    1. Personalizes to their industry and role
    2. Highlights relevant value propositions
    3. Includes a clear call-to-action
    4. Keeps it concise (under 150 words)`;

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${process.env.GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }]
      })
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error?.message || 'Failed to generate email');
    }

    const generatedEmail = data.candidates?.[0]?.content?.parts?.[0]?.text || 'Unable to generate email';

    res.json({
      success: true,
      email: generatedEmail,
      lead: lead,
      campaign: campaign,
      generatedAt: new Date().toISOString(),
      aiProvider: 'Google Gemini'
    });

  } catch (error) {
    console.error('❌ Email generation error:', error);
    res.status(500).json({
      error: 'Failed to generate email',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// AI-powered lead scoring endpoint
app.post('/api/score-lead', async (req, res) => {
  console.log('🎯 Lead scoring requested');

  if (!process.env.GEMINI_API_KEY) {
    return res.status(503).json({
      error: 'AI service not configured',
      message: 'Gemini API key is missing'
    });
  }

  try {
    const { lead } = req.body;

    if (!lead) {
      return res.status(400).json({
        error: 'Missing required field',
        message: 'Lead data is required'
      });
    }

    const prompt = `Analyze this lead and provide a score from 1-100 based on sales potential:

    Lead Details:
    - Name: ${lead.name || 'Unknown'}
    - Company: ${lead.company || 'Unknown'}
    - Industry: ${lead.industry || 'Unknown'}
    - Role: ${lead.role || 'Unknown'}
    - Company Size: ${lead.companySize || 'Unknown'}
    - Budget: ${lead.budget || 'Unknown'}
    - Timeline: ${lead.timeline || 'Unknown'}
    - Pain Points: ${lead.painPoints || 'Unknown'}

    Provide:
    1. A score (1-100)
    2. Brief reasoning (2-3 sentences)
    3. Key strengths
    4. Areas of concern
    5. Recommended next steps

    Format as JSON with fields: score, reasoning, strengths, concerns, nextSteps`;

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${process.env.GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }]
      })
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error?.message || 'Failed to score lead');
    }

    const aiResponse = data.candidates?.[0]?.content?.parts?.[0]?.text || '{"score": 50, "reasoning": "Unable to analyze lead"}';

    // Try to parse JSON response, fallback to simple score if parsing fails
    let scoreData;
    try {
      scoreData = JSON.parse(aiResponse);
    } catch (parseError) {
      scoreData = {
        score: 50,
        reasoning: aiResponse,
        strengths: ['Requires manual review'],
        concerns: ['AI analysis incomplete'],
        nextSteps: ['Manual qualification needed']
      };
    }

    res.json({
      success: true,
      leadScore: scoreData,
      lead: lead,
      scoredAt: new Date().toISOString(),
      aiProvider: 'Google Gemini'
    });

  } catch (error) {
    console.error('❌ Lead scoring error:', error);
    res.status(500).json({
      error: 'Failed to score lead',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// AI-powered campaign strategy endpoint
app.post('/api/generate-strategy', async (req, res) => {
  console.log('📊 Campaign strategy generation requested');

  if (!process.env.GEMINI_API_KEY) {
    return res.status(503).json({
      error: 'AI service not configured',
      message: 'Gemini API key is missing'
    });
  }

  try {
    const { leads, campaignGoals } = req.body;

    if (!leads || !campaignGoals) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'Both leads and campaignGoals are required'
      });
    }

    const leadsCount = Array.isArray(leads) ? leads.length : 1;
    const leadsSummary = Array.isArray(leads) ?
      leads.slice(0, 3).map(lead => `${lead.name || 'Unknown'} at ${lead.company || 'Unknown'} (${lead.industry || 'Unknown'})`).join(', ') :
      `${leads.name || 'Unknown'} at ${leads.company || 'Unknown'}`;

    const prompt = `Create a comprehensive sales campaign strategy:

    Campaign Goals: ${JSON.stringify(campaignGoals)}
    Target Leads: ${leadsCount} leads including ${leadsSummary}

    Generate a strategic plan with:
    1. Campaign Overview (2-3 sentences)
    2. Target Audience Analysis
    3. Key Messaging Themes (3-4 themes)
    4. Recommended Channels (email, phone, social, etc.)
    5. Timeline & Sequence (suggested touchpoints)
    6. Success Metrics to track
    7. Personalization Strategies

    Format as JSON with clear sections.`;

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${process.env.GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }]
      })
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error?.message || 'Failed to generate strategy');
    }

    const aiResponse = data.candidates?.[0]?.content?.parts?.[0]?.text || 'Unable to generate strategy';

    // Try to parse JSON response, fallback to text if parsing fails
    let strategyData;
    try {
      strategyData = JSON.parse(aiResponse);
    } catch (parseError) {
      strategyData = {
        overview: aiResponse,
        targetAudience: 'Requires manual analysis',
        messaging: ['AI-generated strategy available'],
        channels: ['Email', 'Phone'],
        timeline: 'Standard 7-touch sequence',
        metrics: ['Response rate', 'Conversion rate'],
        personalization: ['Industry-specific messaging']
      };
    }

    res.json({
      success: true,
      strategy: strategyData,
      campaignGoals: campaignGoals,
      leadsAnalyzed: leadsCount,
      generatedAt: new Date().toISOString(),
      aiProvider: 'Google Gemini'
    });

  } catch (error) {
    console.error('❌ Strategy generation error:', error);
    res.status(500).json({
      error: 'Failed to generate strategy',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('❌ Server error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: err.message,
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not found',
    message: `Route ${req.originalUrl} not found`,
    availableEndpoints: ['/', '/health', '/api/docs']
  });
});

// Start server
const server = app.listen(port, '0.0.0.0', () => {
  console.log(`🚀 AI SDR Backend running on port ${port}`);
  console.log(`🌍 Server listening on 0.0.0.0:${port}`);
  console.log(`❤️  Health Check: http://localhost:${port}/health`);
  console.log(`📚 API Documentation: http://localhost:${port}/api/docs`);
  console.log(`✅ Server started successfully!`);
});

// Error handling
server.on('error', (error) => {
  console.error('❌ Server error:', error);
  if (error.code === 'EADDRINUSE') {
    console.error(`❌ Port ${port} is already in use`);
  }
  process.exit(1);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

module.exports = app;
