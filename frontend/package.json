{"name": "ai-sdr-frontend", "version": "1.0.0", "description": "Frontend application for AI SDR Agent", "private": true, "dependencies": {"@reduxjs/toolkit": "^1.9.5", "axios": "^1.4.0", "lucide-react": "^0.220.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.43.9", "react-query": "^3.39.3", "react-redux": "^8.0.5", "react-router-dom": "^6.10.0", "react-scripts": "5.0.1", "recharts": "^2.6.2"}, "devDependencies": {"@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^5.59.0", "@typescript-eslint/parser": "^5.59.0", "autoprefixer": "^10.4.14", "eslint": "^8.40.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.23", "tailwindcss": "^3.3.2", "typescript": "^5.0.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3001", "main": "tailwind.config.js", "keywords": [], "author": "", "license": "ISC"}