const Joi = require('joi');
const { DatabaseService } = require('../shared/utils/database');
const { generateId, logger } = require('../shared/utils/helpers');
const { ValidationError, NotFoundError } = require('../shared/utils/api');

// User validation schema
const userSchema = Joi.object({
  userId: Joi.string().optional(),
  username: Joi.string().required().min(3).max(50).alphanum(),
  email: Joi.string().email().required(),
  passwordHash: Joi.string().required(),
  role: Joi.string().valid('admin', 'user').default('user'),
  status: Joi.string().valid('active', 'inactive', 'suspended').default('active'),
  profile: Joi.object({
    firstName: Joi.string().optional().max(100),
    lastName: Joi.string().optional().max(100),
    company: Joi.string().optional().max(255),
    phone: Joi.string().optional().max(50)
  }).optional(),
  preferences: Joi.object({
    notifications: Joi.boolean().default(true),
    theme: Joi.string().valid('light', 'dark').default('light'),
    timezone: Joi.string().default('UTC')
  }).optional(),
  lastLoginAt: Joi.date().optional(),
  loginCount: Joi.number().min(0).default(0)
});

const updateUserSchema = userSchema.fork(['username', 'email', 'passwordHash'], (schema) => schema.optional());

class UserService extends DatabaseService {
  constructor() {
    super(process.env.USERS_TABLE || 'ai-sdr-dev-users');
  }

  getPrimaryKey() {
    return 'userId';
  }

  /**
   * Create a new user with validation
   * @param {Object} userData - User data
   * @returns {Promise<Object>} Created user
   */
  async createUser(userData) {
    // Validate input
    const validatedData = userSchema.validate(userData);
    if (validatedData.error) {
      throw new ValidationError('Invalid user data', validatedData.error.details);
    }

    const user = validatedData.value;
    
    // Generate ID if not provided
    if (!user.userId) {
      user.userId = generateId();
    }

    // Set default values
    user.loginCount = 0;
    user.preferences = user.preferences || {
      notifications: true,
      theme: 'light',
      timezone: 'UTC'
    };

    logger.info('Creating new user', { userId: user.userId, username: user.username });
    
    return await this.create(user);
  }

  /**
   * Get user by ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} User data
   */
  async getUser(userId) {
    return await this.get(userId);
  }

  /**
   * Get user by username
   * @param {string} username - Username
   * @returns {Promise<Object|null>} User data or null if not found
   */
  async getUserByUsername(username) {
    try {
      const scanOptions = {
        FilterExpression: 'username = :username',
        ExpressionAttributeValues: {
          ':username': username
        }
      };

      const users = await this.scan(scanOptions);
      return users.length > 0 ? users[0] : null;
    } catch (error) {
      logger.error('Failed to get user by username', { username, error: error.message });
      throw error;
    }
  }

  /**
   * Get user by email
   * @param {string} email - Email address
   * @returns {Promise<Object|null>} User data or null if not found
   */
  async getUserByEmail(email) {
    try {
      const scanOptions = {
        FilterExpression: 'email = :email',
        ExpressionAttributeValues: {
          ':email': email
        }
      };

      const users = await this.scan(scanOptions);
      return users.length > 0 ? users[0] : null;
    } catch (error) {
      logger.error('Failed to get user by email', { email, error: error.message });
      throw error;
    }
  }

  /**
   * Update user with validation
   * @param {string} userId - User ID
   * @param {Object} updates - Fields to update
   * @returns {Promise<Object>} Updated user
   */
  async updateUser(userId, updates) {
    // Validate updates
    const validatedUpdates = updateUserSchema.validate(updates);
    if (validatedUpdates.error) {
      throw new ValidationError('Invalid update data', validatedUpdates.error.details);
    }

    logger.info('Updating user', { userId, updates: Object.keys(validatedUpdates.value) });
    
    return await this.update(userId, validatedUpdates.value);
  }

  /**
   * Delete user
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  async deleteUser(userId) {
    logger.info('Deleting user', { userId });
    return await this.delete(userId);
  }

  /**
   * Update user status
   * @param {string} userId - User ID
   * @param {string} status - New status
   * @returns {Promise<Object>} Updated user
   */
  async updateStatus(userId, status) {
    const validStatuses = ['active', 'inactive', 'suspended'];
    if (!validStatuses.includes(status)) {
      throw new ValidationError(`Invalid status. Must be one of: ${validStatuses.join(', ')}`);
    }

    logger.info('Updating user status', { userId, status });
    
    return await this.update(userId, { status });
  }

  /**
   * Record user login
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Updated user
   */
  async recordLogin(userId) {
    const user = await this.get(userId);
    const updates = {
      lastLoginAt: new Date().toISOString(),
      loginCount: (user.loginCount || 0) + 1
    };

    logger.info('Recording user login', { userId, loginCount: updates.loginCount });
    
    return await this.update(userId, updates);
  }

  /**
   * Update user profile
   * @param {string} userId - User ID
   * @param {Object} profileData - Profile data to update
   * @returns {Promise<Object>} Updated user
   */
  async updateProfile(userId, profileData) {
    const user = await this.get(userId);
    const currentProfile = user.profile || {};
    const updatedProfile = { ...currentProfile, ...profileData };

    logger.info('Updating user profile', { userId });
    
    return await this.update(userId, { profile: updatedProfile });
  }

  /**
   * Update user preferences
   * @param {string} userId - User ID
   * @param {Object} preferences - Preferences to update
   * @returns {Promise<Object>} Updated user
   */
  async updatePreferences(userId, preferences) {
    const user = await this.get(userId);
    const currentPreferences = user.preferences || {};
    const updatedPreferences = { ...currentPreferences, ...preferences };

    logger.info('Updating user preferences', { userId });
    
    return await this.update(userId, { preferences: updatedPreferences });
  }

  /**
   * Get all users with optional filtering and pagination
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Users with pagination metadata
   */
  async getUsers(options = {}) {
    const { 
      filter = {}, 
      sort = 'createdAt', 
      order = 'desc', 
      page = 1, 
      limit = 20 
    } = options;

    logger.info('Fetching users', { filter, sort, order, page, limit });

    const scanOptions = {};
    
    // Add filter expressions if provided
    if (Object.keys(filter).length > 0) {
      const filterExpressions = [];
      const expressionAttributeNames = {};
      const expressionAttributeValues = {};
      
      Object.entries(filter).forEach(([key, value], index) => {
        const attrName = `#attr${index}`;
        const attrValue = `:val${index}`;
        
        filterExpressions.push(`${attrName} = ${attrValue}`);
        expressionAttributeNames[attrName] = key;
        expressionAttributeValues[attrValue] = value;
      });
      
      if (filterExpressions.length > 0) {
        scanOptions.FilterExpression = filterExpressions.join(' AND ');
        scanOptions.ExpressionAttributeNames = expressionAttributeNames;
        scanOptions.ExpressionAttributeValues = expressionAttributeValues;
      }
    }

    const items = await this.scan(scanOptions);
    
    // Remove sensitive data
    const sanitizedItems = items.map(user => {
      const { passwordHash, ...sanitizedUser } = user;
      return sanitizedUser;
    });
    
    // Sort items
    sanitizedItems.sort((a, b) => {
      const aVal = a[sort];
      const bVal = b[sort];
      
      if (order === 'desc') {
        return bVal > aVal ? 1 : -1;
      } else {
        return aVal > bVal ? 1 : -1;
      }
    });

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedItems = sanitizedItems.slice(startIndex, endIndex);

    return {
      users: paginatedItems,
      pagination: {
        total: sanitizedItems.length,
        page,
        limit,
        totalPages: Math.ceil(sanitizedItems.length / limit),
        hasNext: endIndex < sanitizedItems.length,
        hasPrev: page > 1
      }
    };
  }
}

module.exports = {
  UserService,
  userSchema,
  updateUserSchema
};
