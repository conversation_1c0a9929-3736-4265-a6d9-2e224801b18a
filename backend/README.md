# AI SDR Backend Services

This directory contains the backend services for the AI SDR Agent, built using a serverless microservices architecture on AWS.

## Architecture Overview

The backend is organized into modular services, each responsible for specific functionality:

- **Authentication Service** (`auth/`) - User management and JWT authentication
- **Lead Management Service** (`leads/`) - Lead CRUD operations and scoring
- **Outreach Service** (`outreach/`) - Multi-channel campaign execution
- **Analytics Service** (`analytics/`) - Metrics and reporting
- **Integrations Service** (`integrations/`) - CRM connectivity
- **Shared Utilities** (`shared/`) - Common models, utilities, and helpers

## Service Structure

Each service follows a consistent structure:

```
service/
├── handler.js              # Lambda function handlers
├── tests/
│   ├── unit/               # Unit tests
│   └── integration/        # Integration tests
└── README.md               # Service-specific documentation
```

## Shared Components

The `shared/` directory contains reusable components:

```
shared/
├── models/                 # Data models (Lead, Campaign, User)
│   ├── lead.js
│   ├── campaign.js
│   └── user.js
└── utils/                  # Utility modules
    ├── api.js              # API helpers and error handling
    ├── database.js         # DynamoDB utilities
    ├── email.js            # Email service (SendGrid)
    ├── sms.js              # SMS service (Twilio)
    ├── crm.js              # CRM integrations
    ├── analytics.js        # Analytics utilities
    ├── config.js           # Configuration management
    ├── helpers.js          # General helpers
    └── testHelpers.js      # Testing utilities
```

## Key Features

### Modular Design
- Each service is independently deployable
- Shared utilities promote code reuse
- Clear separation of concerns

### Error Handling
- Custom error classes with proper HTTP status codes
- Comprehensive logging with Winston
- Lambda wrapper for consistent error responses

### Validation
- Joi schemas for input validation
- Type checking and sanitization
- Detailed validation error messages

### Database Integration
- Generic DatabaseService base class
- DynamoDB optimized operations
- Proper error handling and retries

### Configuration Management
- AWS Secrets Manager integration
- Parameter Store support
- Environment-based configuration
- Secure credential handling

### Testing
- Jest test framework
- Comprehensive test helpers
- Unit and integration tests
- Mock services for external dependencies

## API Endpoints

### Authentication (`/auth`)
- `POST /auth/login` - User login
- `POST /auth/register` - User registration
- `POST /auth/refresh` - Token refresh
- `GET /auth/verify` - Token verification

### Leads (`/leads`)
- `GET /leads` - List leads with filtering
- `POST /leads` - Create new lead
- `GET /leads/{id}` - Get lead by ID
- `PUT /leads/{id}` - Update lead
- `DELETE /leads/{id}` - Delete lead
- `POST /leads/import` - Bulk import leads

### Outreach (`/outreach`)
- `POST /outreach/campaign` - Execute campaign
- `POST /outreach/email` - Send email
- `POST /outreach/sms` - Send SMS
- `GET /outreach/status/{campaignId}` - Campaign status

### Analytics (`/analytics`)
- `GET /analytics/dashboard` - Dashboard metrics
- `GET /analytics/metrics` - Custom metrics
- `POST /analytics/report` - Generate report

### Integrations (`/integrations`)
- `POST /integrations/connect` - Connect CRM
- `POST /integrations/sync` - Sync leads
- `GET /integrations/status/{crmType}` - Connection status

## Development Setup

### Prerequisites
- Node.js 18+
- AWS CLI configured
- Serverless Framework

### Installation
```bash
npm install
```

### Environment Configuration
```bash
cp .env.example .env
# Edit .env with your configuration
```

### Running Tests
```bash
npm test                    # All tests
npm run test:unit          # Unit tests only
npm run test:integration   # Integration tests only
npm run test:coverage      # With coverage report
```

### Local Development
```bash
npm run dev                # Start local development server
npm run offline            # Run serverless offline
```

### Deployment
```bash
npm run deploy:dev         # Deploy to development
npm run deploy:staging     # Deploy to staging
npm run deploy:prod        # Deploy to production
```

## Configuration

### Required AWS Resources
- DynamoDB tables (users, leads, campaigns, jobs)
- Secrets Manager secrets
- Parameter Store parameters
- CloudWatch log groups

### Environment Variables
- `STAGE` - Deployment stage
- `AWS_REGION` - AWS region
- `LOG_LEVEL` - Logging level

### Secrets (AWS Secrets Manager)
- `ai-sdr/auth` - JWT secret
- `ai-sdr/sendgrid` - SendGrid API key
- `ai-sdr/twilio` - Twilio credentials
- `ai-sdr/hubspot` - HubSpot API credentials

## Data Models

### Lead
```javascript
{
  leadId: string,
  companyName: string,
  contactEmail: string,
  contactName: string,
  phone: string,
  revenue: number,
  industry: string,
  location: string,
  score: number (0-100),
  emailStatus: enum,
  tags: array,
  createdAt: timestamp,
  updatedAt: timestamp
}
```

### Campaign
```javascript
{
  campaignId: string,
  name: string,
  status: enum,
  channels: array,
  leads: array,
  template: object,
  settings: object,
  createdAt: timestamp,
  updatedAt: timestamp
}
```

### User
```javascript
{
  userId: string,
  username: string,
  email: string,
  role: enum,
  status: enum,
  profile: object,
  preferences: object,
  createdAt: timestamp,
  updatedAt: timestamp
}
```

## Error Handling

The system uses custom error classes:
- `ValidationError` (400) - Input validation failures
- `UnauthorizedError` (401) - Authentication failures
- `ForbiddenError` (403) - Authorization failures
- `NotFoundError` (404) - Resource not found
- `InternalServerError` (500) - System errors

## Logging

Structured logging with Winston:
- Request/response logging
- Error tracking
- Performance metrics
- Debug information

## Security

- JWT-based authentication
- Password hashing with bcrypt
- Input validation and sanitization
- CORS configuration
- Rate limiting (planned)

## Performance

- Connection pooling for DynamoDB
- Caching for configuration
- Retry logic with exponential backoff
- Batch operations for bulk processing

## Monitoring

- CloudWatch metrics
- Custom application metrics
- Error tracking
- Performance monitoring

## Contributing

1. Follow the established patterns
2. Add tests for new functionality
3. Update documentation
4. Ensure all tests pass
5. Follow the coding standards

## Troubleshooting

### Common Issues
1. **DynamoDB connection errors** - Check AWS credentials and region
2. **Validation errors** - Review input schemas
3. **Authentication failures** - Verify JWT configuration
4. **External service errors** - Check API keys and credentials

### Debug Mode
Set `LOG_LEVEL=debug` for detailed logging.

### Testing
Run specific test suites to isolate issues:
```bash
npm test -- --testPathPattern=auth
npm test -- --testPathPattern=leads
```
