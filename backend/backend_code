# This file contains the Python code for the AWS Lambda functions
# outlined in the "AI SDR Agent: Phase 2 Backend Implementation Plan".
# It provides the core logic for CSV parsing, lead scoring,
# message generation, email sending, and analytics aggregation.

# IMPORTANT:
# - Replace placeholder API keys with actual values from AWS Secrets Manager in a production environment.
# - Implement robust error handling and logging for all functions.
# - Ensure IAM roles for each Lambda have the necessary permissions (DynamoDB access, S3 access, SQS access, Secrets Manager access, outbound network access).
# - This code assumes a DynamoDB table named 'LeadsTable' and an SQS queue named 'OutreachEmailQueue' are already provisioned.
# - For actual external API calls (Relevance AI, SendGrid), you would install their respective SDKs
#   and use them here. For now, placeholders for API calls are used.

import json
import os
import csv
import io
import uuid
import boto3
from datetime import datetime

# Initialize AWS clients
s3_client = boto3.client('s3')
dynamodb = boto3.resource('dynamodb')
sqs = boto3.client('sqs')
secretsmanager = boto3.client('secretsmanager')

# --- Helper function to get secrets securely (MANDATORY) ---
def get_secret(secret_name):
    """Retrieves a secret from AWS Secrets Manager."""
    try:
        get_secret_value_response = secretsmanager.get_secret_value(SecretId=secret_name)
        if 'SecretString' in get_secret_value_response:
            return json.loads(get_secret_value_response['SecretString'])
        else:
            return get_secret_value_response['SecretBinary'].decode('utf-8')
    except Exception as e:
        print(f"Error retrieving secret '{secret_name}': {e}")
        raise

# Placeholder for Secrets Manager Names (replace with actual names)
RELEVANCE_AI_SECRET_NAME = os.environ.get('RELEVANCE_AI_SECRET_NAME', 'relevance_ai_api_key_secret')
SENDGRID_SECRET_NAME = os.environ.get('SENDGRID_SECRET_NAME', 'sendgrid_api_key_secret')
GOOGLE_GEMINI_SECRET_NAME = os.environ.get('GOOGLE_GEMINI_SECRET_NAME', 'google_gemini_api_key_secret')

# DynamoDB table name (configured via environment variable in serverless.yml)
LEADS_TABLE_NAME = os.environ.get('LEADS_TABLE_NAME', 'LeadsTable')
ANALYTICS_TABLE_NAME = os.environ.get('ANALYTICS_TABLE_NAME', 'AnalyticsMetricsTable')
OUTREACH_EMAIL_QUEUE_URL = os.environ.get('OUTREACH_EMAIL_QUEUE_URL', 'https://sqs.us-east-1.amazonaws.com/123456789012/OutreachEmailQueue') # Replace with actual SQS Queue URL


# --- 1. Robust CSV Parsing and Bulk Lead Insertion (`ImportLeadsLambda`) ---

def import_leads_lambda(event, context):
    """
    Lambda function to process CSV files uploaded to S3 and bulk insert leads into DynamoDB.
    Triggered by S3 PutObject event.
    """
    print(f"Received S3 event: {json.dumps(event)}")

    for record in event['Records']:
        bucket_name = record['s3']['bucket']['name']
        file_key = record['s3']['object']['key']
        user_id = file_key.split('/')[2] # Assuming path is artifacts/{appId}/users/{userId}/imports/{filename}
        app_id = file_key.split('/')[1] # Assuming path is artifacts/{appId}/users/{userId}/imports/{filename}

        print(f"Processing file {file_key} from bucket {bucket_name} for user {user_id}")

        try:
            # 1. Read CSV file from S3
            response = s3_client.get_object(Bucket=bucket_name, Key=file_key)
            csv_content = response['Body'].read().decode('utf-8')
            csv_file = io.StringIO(csv_content)
            reader = csv.DictReader(csv_file)

            leads_to_insert = []
            valid_headers = ['companyName', 'score', 'status', 'lastContacted']

            # Basic header validation
            if not all(header in reader.fieldnames for header in valid_headers):
                print(f"Error: Missing required headers in CSV. Expected: {valid_headers}, Found: {reader.fieldnames}")
                # Log this error and potentially notify the user
                continue # Skip to next record if headers are invalid

            for row_num, row in enumerate(reader, start=2): # Start counting from line 2 (after headers)
                try:
                    lead = {
                        'id': str(uuid.uuid4()), # Generate unique ID for each lead
                        'userId': user_id,
                        'appId': app_id,
                        'companyName': row.get('companyName', '').strip(),
                        'score': int(row.get('score', 0)), # Default to 0 if not present or invalid
                        'status': row.get('status', 'New').strip(),
                        'lastContacted': row.get('lastContacted', datetime.now().isoformat().split('T')[0]).strip() # Default to today
                    }

                    # Basic data validation for required fields
                    if not lead['companyName']:
                        print(f"Skipping row {row_num}: 'companyName' is required and missing.")
                        continue

                    # Validate status against allowed values (optional but good practice)
                    allowed_statuses = ['New', 'Contacted', 'Qualified', 'Disqualified']
                    if lead['status'] not in allowed_statuses:
                        print(f"Warning: Row {row_num} has invalid status '{lead['status']}'. Defaulting to 'New'.")
                        lead['status'] = 'New'

                    leads_to_insert.append(lead)

                except ValueError as ve:
                    print(f"Skipping row {row_num} due to data conversion error: {ve} - Row: {row}")
                except Exception as e:
                    print(f"Skipping row {row_num} due to unexpected error: {e} - Row: {row}")

            # 5. Use DynamoDB's batch_write_item operation for efficient bulk insertion
            if leads_to_insert:
                table = dynamodb.Table(LEADS_TABLE_NAME)
                with table.batch_writer() as batch:
                    for lead_item in leads_to_insert:
                        batch.put_item(Item=lead_item)
                print(f"Successfully processed and inserted {len(leads_to_insert)} leads for user {user_id}.")
            else:
                print(f"No valid leads found in file {file_key} for user {user_id}.")

        except s3_client.exceptions.NoSuchKey:
            print(f"Error: File {file_key} not found in bucket {bucket_name}")
        except Exception as e:
            print(f"An error occurred during CSV processing for {file_key}: {e}")
            # In a real app, you might publish an error message to an SQS dead-letter queue or SNS topic

    return {
        'statusCode': 200,
        'body': json.dumps('CSV processing initiated.')
    }


# --- 2. AI-Driven Lead Scoring (`ScoreLeadLambda`) ---

def score_lead_lambda(event, context):
    """
    Lambda function to score leads using Relevance AI (placeholder)
    Triggered by DynamoDB Stream from LeadsTable.
    """
    print(f"Received DynamoDB Stream event: {json.dumps(event)}")

    # Retrieve Relevance AI API key from Secrets Manager
    # relevance_ai_secrets = get_secret(RELEVANCE_AI_SECRET_NAME)
    # relevance_ai_api_key = relevance_ai_secrets['api_key'] # Assuming 'api_key' is a key in your secret

    table = dynamodb.Table(LEADS_TABLE_NAME)

    for record in event['Records']:
        if record['eventName'] in ['INSERT', 'MODIFY']:
            new_image = record['dynamodb']['NewImage']
            lead_id = new_image['id']['S']
            company_name = new_image['companyName']['S']
            user_id = new_image['userId']['S'] # For private data path

            print(f"Processing lead ID: {lead_id}, Company: {company_name}")

            try:
                # --- Placeholder for Relevance AI API call ---
                # In a real application, you'd use Relevance AI's SDK or API directly here.
                # Example:
                # response = requests.post(
                #     "https://api.relevance.ai/v1/score_lead",
                #     headers={"Authorization": f"Bearer {relevance_ai_api_key}"},
                #     json={"company_name": company_name, "user_id": user_id}
                # )
                # response.raise_for_status() # Raise an HTTPError for bad responses (4xx or 5xx)
                # relevance_score = response.json().get('score', 0)

                # Simulate a score for demonstration
                relevance_score = len(company_name) * 5 % 100 + 1 # Simple dummy scoring logic
                if relevance_score < 10: relevance_score += 10 # Ensure minimum score

                # Update the lead in DynamoDB with the new score
                table.update_item(
                    Key={'id': lead_id},
                    UpdateExpression="SET score = :s, scoredAt = :sa",
                    ExpressionAttributeValues={
                        ':s': int(relevance_score),
                        ':sa': datetime.now().isoformat()
                    }
                )
                print(f"Successfully scored lead {lead_id} with score {relevance_score}")

            except Exception as e:
                print(f"Error scoring lead {lead_id}: {e}")
                # Implement retry logic or dead-letter queue for failed scoring

    return {
        'statusCode': 200,
        'body': json.dumps('Lead scoring process completed.')
    }


# --- 3. Google Gemini API Calls for Message Generation (`GenerateMessageLambda`) ---

def generate_message_lambda(event, context):
    """
    Lambda function to generate personalized outreach messages using Google Gemini.
    Triggered by API Gateway POST request.
    """
    print(f"Received API Gateway event for message generation: {json.dumps(event)}")

    try:
        body = json.loads(event['body'])
        lead_id = body.get('leadId')
        prompt = body.get('prompt')
        # Assuming recipient_email is also provided or can be derived
        recipient_email = body.get('recipientEmail', '<EMAIL>') # Placeholder

        if not lead_id or not prompt:
            return {
                'statusCode': 400,
                'body': json.dumps({'error': 'Missing leadId or prompt in request body.'})
            }

        # Retrieve Google Gemini API key from Secrets Manager
        gemini_secrets = get_secret(GOOGLE_GEMINI_SECRET_NAME)
        gemini_api_key = gemini_secrets['api_key'] # Assuming 'api_key' is a key in your secret

        # Get lead details from DynamoDB
        leads_table = dynamodb.Table(LEADS_TABLE_NAME)
        response = leads_table.get_item(Key={'id': lead_id})
        lead_data = response.get('Item')

        if not lead_data:
            return {
                'statusCode': 404,
                'body': json.dumps({'error': f'Lead with ID {lead_id} not found.'})
            }

        company_name = lead_data.get('companyName', 'their company')
        lead_score = lead_data.get('score', 'N/A')
        lead_status = lead_data.get('status', 'N/A')

        # Construct the prompt for Gemini
        llm_prompt = (
            f"Generate a personalized outreach email for {company_name}. "
            f"Their current lead score is {lead_score} and status is {lead_status}. "
            f"Key focus for the email: \"{prompt}\". "
            "The email should be professional, concise, and highlight how our mining equipment solutions "
            "can specifically benefit their operations. Provide a clear subject line and email body."
            "The email should not include placeholders like [Your Name] or [Your Company]. Make it ready to send."
        )

        # --- Placeholder for Google Gemini API call ---
        # In a real application, you would make an HTTP request to the Gemini API endpoint.
        # Example using requests (install 'requests' library in Lambda layer):
        # import requests
        # gemini_api_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent"
        # headers = {'Content-Type': 'application/json'}
        # payload = {
        #     "contents": [{"role": "user", "parts": [{"text": llm_prompt}]}],
        #     "generationConfig": {"temperature": 0.7, "maxOutputTokens": 500}
        # }
        # gemini_response = requests.post(f"{gemini_api_url}?key={gemini_api_key}", headers=headers, json=payload)
        # gemini_response.raise_for_status()
        # generated_content = gemini_response.json().get('candidates', [])[0].get('content', {}).get('parts', [])[0].get('text', 'No message generated.')

        # Simulate Gemini response for demonstration
        generated_content = f"Subject: Tailored Mining Solutions for {company_name}\n\nDear Team at {company_name},\n\nI hope this email finds you well. Given your score of {lead_score} and current status as {lead_status}, we believe our cutting-edge mining equipment can significantly enhance your operations. Based on your focus: \"{prompt}\", our solutions are designed to deliver exceptional efficiency and cost savings. \n\nLet's connect to discuss how we can help you achieve your goals.\n\nBest regards,\nAI SDR Agent"

        return {
            'statusCode': 200,
            'body': json.dumps({
                'generatedMessage': generated_content,
                'leadId': lead_id,
                'recipientEmail': recipient_email # Return recipient for frontend to use in send
            })
        }

    except Exception as e:
        print(f"Error in GenerateMessageLambda: {e}")
        return {
            'statusCode': 500,
            'body': json.dumps({'error': f'Internal server error during message generation: {e}'})
        }


# --- 4. Integrating SendGrid for Email Sending and Tracking ---

# Lambda Function (`EnqueueEmailLambda`)
def enqueue_email_lambda(event, context):
    """
    Lambda function to receive email payloads from API Gateway and enqueue them to SQS.
    Triggered by API Gateway POST request.
    """
    print(f"Received API Gateway event for email enqueue: {json.dumps(event)}")

    try:
        body = json.loads(event['body'])
        lead_id = body.get('leadId')
        recipient_email = body.get('recipientEmail')
        subject = body.get('subject')
        message_body = body.get('messageBody')

        if not all([lead_id, recipient_email, subject, message_body]):
            return {
                'statusCode': 400,
                'body': json.dumps({'error': 'Missing required fields for email enqueue.'})
            }

        # Publish message to SQS queue
        sqs.send_message(
            QueueUrl=OUTREACH_EMAIL_QUEUE_URL,
            MessageBody=json.dumps({
                'leadId': lead_id,
                'recipientEmail': recipient_email,
                'subject': subject,
                'messageBody': message_body,
                'timestamp': datetime.now().isoformat()
            })
        )

        print(f"Successfully enqueued email for lead {lead_id} to {recipient_email}")
        return {
            'statusCode': 200,
            'body': json.dumps({'message': 'Email enqueued successfully.'})
        }

    except Exception as e:
        print(f"Error in EnqueueEmailLambda: {e}")
        return {
            'statusCode': 500,
            'body': json.dumps({'error': f'Internal server error during email enqueue: {e}'})
        }


# Lambda Function (`SendEmailLambda`)
def send_email_lambda(event, context):
    """
    Lambda function to consume messages from SQS and send emails via SendGrid.
    Triggered by SQS queue.
    """
    print(f"Received SQS event for email sending: {json.dumps(event)}")

    # Retrieve SendGrid API key from Secrets Manager
    # sendgrid_secrets = get_secret(SENDGRID_SECRET_NAME)
    # sendgrid_api_key = sendgrid_secrets['api_key'] # Assuming 'api_key' is a key in your secret

    leads_table = dynamodb.Table(LEADS_TABLE_NAME)

    for record in event['Records']:
        try:
            message_body = json.loads(record['body'])
            lead_id = message_body.get('leadId')
            recipient_email = message_body.get('recipientEmail')
            subject = message_body.get('subject')
            message_content = message_body.get('messageBody')

            if not all([lead_id, recipient_email, subject, message_content]):
                print(f"Skipping malformed SQS message: {message_body}")
                continue # Skip to next message if required fields are missing

            # --- Placeholder for SendGrid API call ---
            # In a real application, you'd use SendGrid's Python SDK or API directly here.
            # Example using sendgrid-python (install 'sendgrid' library in Lambda layer):
            # from sendgrid import SendGridAPIClient
            # from sendgrid.helpers.mail import Mail
            # message = Mail(
            #     from_email='<EMAIL>', # Replace with your SendGrid verified sender
            #     to_emails=recipient_email,
            #     subject=subject,
            #     html_content=message_content) # Use html_content for rich text emails
            # sg = SendGridAPIClient(sendgrid_api_key)
            # sg_response = sg.send(message)
            # print(f"SendGrid API response status code: {sg_response.status_code}")
            # print(f"SendGrid API response body: {sg_response.body}")

            # Simulate email sending success
            print(f"Simulating sending email to {recipient_email} for lead {lead_id} with subject: {subject}")

            # Update lead status in DynamoDB (e.g., to 'Contacted')
            leads_table.update_item(
                Key={'id': lead_id},
                UpdateExpression="SET #s = :status, lastContacted = :lc",
                ExpressionAttributeNames={'#s': 'status'}, # '#s' is an alias for 'status' as 'status' is a reserved keyword in DynamoDB
                ExpressionAttributeValues={
                    ':status': 'Contacted',
                    ':lc': datetime.now().isoformat().split('T')[0]
                }
            )
            print(f"Successfully sent email and updated lead status for {lead_id}.")

        except Exception as e:
            print(f"Error processing SQS message for email sending: {e}")
            # This message will be returned to the SQS queue based on Lambda's default retry behavior.
            # If after retries it still fails, it will go to the Dead-Letter Queue (if configured).

    return {
        'statusCode': 200,
        'body': json.dumps('Email sending process completed.')
    }


# --- 5. Advanced Outreach Logic (Sequences) - Backend Foundation (No direct code for full sequence processing yet) ---
# This section primarily outlines the architectural components required, as detailed in the plan.
# Full implementation of a sequence processor Lambda would involve more complex state management
# within DynamoDB and EventBridge scheduling.

# Example structure for EventBridge triggered Lambda to process sequences
def sequence_processor_lambda(event, context):
    """
    Placeholder Lambda for processing outreach sequences.
    Triggered by AWS EventBridge periodically.
    This function would check LeadOutreachStatusTable for leads whose next step is due,
    personalize messages, enqueue emails, and update lead status for next steps.
    """
    print(f"Received EventBridge scheduled event for sequence processing: {json.dumps(event)}")
    # Logic to query LeadOutreachStatusTable for leads needing a follow-up
    # Example:
    # lead_outreach_status_table = dynamodb.Table('LeadOutreachStatusTable')
    # due_leads = lead_outreach_status_table.query(
    #     IndexName='nextTriggerDate-index', # Requires a GSI on nextTriggerDate
    #     KeyConditionExpression=Key('nextTriggerDate').lt(datetime.now().isoformat())
    # )
    # for lead_status in due_leads['Items']:
    #     # Determine next step, generate message (calling GenerateMessageLambda logic),
    #     # enqueue email (calling EnqueueEmailLambda logic), update LeadOutreachStatusTable
    print("Sequence processing logic would run here. (Future Phase 3)")
    return {
        'statusCode': 200,
        'body': json.dumps('Sequence processing initiated (placeholder).')
    }


# --- 6. Analytics Integration (`AggregateAnalyticsLambda` & `GetDashboardMetricsLambda`) ---

# Lambda Function (`AggregateAnalyticsLambda`)
def aggregate_analytics_lambda(event, context):
    """
    Lambda function to aggregate analytics data from LeadsTable.
    Triggered by AWS EventBridge (e.g., daily).
    """
    print(f"Received EventBridge scheduled event for analytics aggregation: {json.dumps(event)}")

    leads_table = dynamodb.Table(LEADS_TABLE_NAME)
    analytics_table = dynamodb.Table(ANALYTICS_TABLE_NAME)

    try:
        # Scan LeadsTable (for small datasets, use Query with GSI for large)
        response = leads_table.scan(Select='COUNT', ReturnConsumedCapacity='TOTAL')
        total_leads = response['ScannedCount']

        # Get counts by status (requires more specific queries/scans or GSIs for efficiency)
        new_leads_count = leads_table.query(
            IndexName='status-index', # Requires GSI on status
            KeyConditionExpression=boto3.dynamodb.conditions.Key('status').eq('New'),
            Select='COUNT'
        )['Count'] if 'status-index' in leads_table.global_secondary_indexes else 0

        # Simulate emails sent for now, or fetch from a real email log if available
        emails_sent_today = 100 # Placeholder

        # Calculate a dummy conversion rate
        qualified_leads_count = leads_table.query(
            IndexName='status-index', # Requires GSI on status
            KeyConditionExpression=boto3.dynamodb.conditions.Key('status').eq('Qualified'),
            Select='COUNT'
        )['Count'] if 'status-index' in leads_table.global_secondary_indexes else 0

        conversion_rate = (qualified_leads_count / total_leads * 100) if total_leads > 0 else 0

        current_date = datetime.now().isoformat().split('T')[0] # YYYY-MM-DD

        # Store aggregated metrics
        analytics_table.put_item(
            Item={
                'date': current_date,
                'totalLeads': total_leads,
                'newLeadsCount': new_leads_count,
                'qualifiedLeadsCount': qualified_leads_count,
                'emailsSent': emails_sent_today, # Replace with actual email sent count
                'conversionRate': round(conversion_rate, 2)
            }
        )
        print(f"Analytics aggregated for {current_date}: Total Leads={total_leads}, Conversion Rate={conversion_rate}%")

    except Exception as e:
        print(f"Error during analytics aggregation: {e}")

    return {
        'statusCode': 200,
        'body': json.dumps('Analytics aggregation initiated (placeholder).')
    }

# Lambda Function (`GetDashboardMetricsLambda`)
def get_dashboard_metrics_lambda(event, context):
    """
    Lambda function to fetch aggregated analytics data for the dashboard.
    Triggered by API Gateway GET request.
    """
    print(f"Received API Gateway event for dashboard metrics: {json.dumps(event)}")

    analytics_table = dynamodb.Table(ANALYTICS_TABLE_NAME)
    current_date = datetime.now().isoformat().split('T')[0]

    try:
        response = analytics_table.get_item(Key={'date': current_date})
        metrics = response.get('Item')

        if not metrics:
            # Fallback or default data if no metrics for today
            metrics = {
                'totalLeads': 0,
                'emailsSent': 0,
                'conversionRate': 0.0
            }
            print(f"No analytics metrics found for {current_date}, returning defaults.")

        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*' # CORS for frontend calls
            },
            'body': json.dumps(metrics)
        }
    except Exception as e:
        print(f"Error fetching dashboard metrics: {e}")
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({'error': f'Internal server error fetching metrics: {e}'})
        }


# --- Serverless Framework (serverless.yml) Configuration Outline ---
# This YAML configuration would define your AWS services and connect them
# to the Python Lambda functions above.
"""
service: ai-sdr-backend

frameworkVersion: '3'

provider:
  name: aws
  runtime: python3.9
  region: us-east-1
  memorySize: 256
  timeout: 30 # Seconds
  environment:
    LEADS_TABLE_NAME: LeadsTable-${sls:stage}
    ANALYTICS_TABLE_NAME: AnalyticsMetricsTable-${sls:stage}
    OUTREACH_EMAIL_QUEUE_URL: !GetQueueUrl OutreachEmailQueue
    RELEVANCE_AI_SECRET_NAME: relevance_ai_api_key_secret # Name in Secrets Manager
    SENDGRID_SECRET_NAME: sendgrid_api_key_secret # Name in Secrets Manager
    GOOGLE_GEMINI_SECRET_NAME: google_gemini_api_key_secret # Name in Secrets Manager
  iam:
    role:
      statements:
        - Effect: "Allow"
          Action:
            - "dynamodb:PutItem"
            - "dynamodb:BatchWriteItem"
            - "dynamodb:GetItem"
            - "dynamodb:UpdateItem"
            - "dynamodb:DeleteItem"
            - "dynamodb:Scan"
            - "dynamodb:Query"
          Resource:
            - "arn:aws:dynamodb:${aws:region}:${aws:accountId}:table/${self:provider.environment.LEADS_TABLE_NAME}"
            - "arn:aws:dynamodb:${aws:region}:${aws:accountId}:table/${self:provider.environment.LEADS_TABLE_NAME}/index/*"
            - "arn:aws:dynamodb:${aws:region}:${aws:accountId}:table/${self:provider.environment.ANALYTICS_TABLE_NAME}"
            - "arn:aws:dynamodb:${aws:region}:${aws:accountId}:table/${self:provider.environment.ANALYTICS_TABLE_NAME}/index/*"
        - Effect: "Allow"
          Action:
            - "s3:GetObject"
            - "s3:PutObject" # For pre-signed URLs
          Resource: "arn:aws:s3:::ai-sdr-import-bucket-${sls:stage}/*"
        - Effect: "Allow"
          Action:
            - "sqs:SendMessage"
            - "sqs:ReceiveMessage"
            - "sqs:DeleteMessage"
            - "sqs:GetQueueAttributes"
          Resource: !GetAtt OutreachEmailQueue.Arn
        - Effect: "Allow"
          Action:
            - "secretsmanager:GetSecretValue"
          Resource:
            - "arn:aws:secretsmanager:${aws:region}:${aws:accountId}:secret:${self:provider.environment.RELEVANCE_AI_SECRET_NAME}*"
            - "arn:aws:secretsmanager:${aws:region}:${aws:accountId}:secret:${self:provider.environment.SENDGRID_SECRET_NAME}*"
            - "arn:aws:secretsmanager:${aws:region}:${aws:accountId}:secret:${self:provider.environment.GOOGLE_GEMINI_SECRET_NAME}*"


functions:
  ImportLeadsLambda:
    handler: handler.import_leads_lambda
    events:
      - s3:
          bucket: ai-sdr-import-bucket-${sls:stage}
          event: s3:ObjectCreated:*
          rules:
            - prefix: artifacts/${sls:stage}/users/ # assuming this prefix for user-specific imports
              suffix: .csv
    # Layers: Add a layer for csv or other specific libraries if not built-in

  ScoreLeadLambda:
    handler: handler.score_lead_lambda
    events:
      - stream:
          type: dynamodb
          arn:
            Fn::GetAtt: [LeadsTable, StreamArn]
          batchSize: 10
          startingPosition: LATEST
    # Layers: Add a layer for requests if making external HTTP calls

  GenerateMessageLambda:
    handler: handler.generate_message_lambda
    events:
      - httpApi:
          path: /outreach/generate-message
          method: POST
          # authorizer: # Uncomment and configure if using Lambda Authorizer / Cognito
          #   name: YourCognitoAuthorizer
    # Layers: Add a layer for requests if making external HTTP calls

  EnqueueEmailLambda:
    handler: handler.enqueue_email_lambda
    events:
      - httpApi:
          path: /outreach/send-email
          method: POST
          # authorizer: # Uncomment and configure if using Lambda Authorizer / Cognito
          #   name: YourCognitoAuthorizer

  SendEmailLambda:
    handler: handler.send_email_lambda
    events:
      - sqs:
          arn: !GetAtt OutreachEmailQueue.Arn
          batchSize: 1
    # Layers: Add a layer for sendgrid SDK

  AggregateAnalyticsLambda:
    handler: handler.aggregate_analytics_lambda
    events:
      - schedule:
          rate: rate(24 hours) # Run daily
          # input: # Optional input for the lambda
    # Layers: Add a layer for requests if making external HTTP calls

  GetDashboardMetricsLambda:
    handler: handler.get_dashboard_metrics_lambda
    events:
      - httpApi:
          path: /analytics/dashboard-metrics
          method: GET
          cors: true # Enable CORS for frontend
          # authorizer: # Uncomment and configure if using Lambda Authorizer / Cognito
          #   name: YourCognitoAuthorizer

resources:
  Resources:
    LeadsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:provider.environment.LEADS_TABLE_NAME}
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S
          - AttributeName: userId
            AttributeType: S
          - AttributeName: status
            AttributeType: S
        KeySchema:
          - AttributeName: id
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST # On-demand pricing
        StreamSpecification:
          StreamViewType: NEW_IMAGE # Needed for ScoreLeadLambda
        GlobalSecondaryIndexes:
          - IndexName: userId-status-index
            KeySchema:
              - AttributeName: userId
                KeyType: HASH
              - AttributeName: status
                KeyType: RANGE
            Projection:
              ProjectionType: ALL
          # Add other GSIs if needed for specific queries (e.g., status-index for aggregate analytics)
          - IndexName: status-index
            KeySchema:
              - AttributeName: status
                KeyType: HASH
            Projection:
              ProjectionType: ALL

    AnalyticsMetricsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:provider.environment.ANALYTICS_TABLE_NAME}
        AttributeDefinitions:
          - AttributeName: date
            AttributeType: S
        KeySchema:
          - AttributeName: date
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST

    OutreachEmailQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: OutreachEmailQueue-${sls:stage}
        RedrivePolicy: # Optional: Configure a Dead-Letter Queue for failed messages
          deadLetterTargetArn: !GetAtt OutreachEmailDLQ.Arn
          maxReceiveCount: 3

    OutreachEmailDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: OutreachEmailDLQ-${sls:stage}

    # S3 Bucket for CSV Imports
    AISDRImportBucket:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: ai-sdr-import-bucket-${sls:stage}
        LifecycleConfiguration:
          Rules:
            - Id: CleanUpOldImports
              Status: Enabled
              ExpirationInDays: 7 # Delete objects after 7 days
"""
