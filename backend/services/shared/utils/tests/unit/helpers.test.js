const {
  generateId,
  generateTimestamp,
  isValidEmail,
  isValidPhone,
  sanitizeString,
  createSlug,
  removeEmptyValues
} = require('../../helpers');

describe('Helpers Utility', () => {
  describe('generateId', () => {
    it('should generate a valid UUID', () => {
      const id = generateId();
      expect(id).toBeDefined();
      expect(typeof id).toBe('string');
      expect(id).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i);
    });

    it('should generate unique IDs', () => {
      const id1 = generateId();
      const id2 = generateId();
      expect(id1).not.toBe(id2);
    });
  });

  describe('generateTimestamp', () => {
    it('should generate a valid ISO timestamp', () => {
      const timestamp = generateTimestamp();
      expect(timestamp).toBeDefined();
      expect(typeof timestamp).toBe('string');
      expect(new Date(timestamp).toISOString()).toBe(timestamp);
    });
  });

  describe('isValidEmail', () => {
    it('should validate correct email addresses', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
    });

    it('should reject invalid email addresses', () => {
      expect(isValidEmail('invalid-email')).toBe(false);
      expect(isValidEmail('test@')).toBe(false);
      expect(isValidEmail('@example.com')).toBe(false);
      expect(isValidEmail('<EMAIL>')).toBe(false);
    });
  });

  describe('isValidPhone', () => {
    it('should validate correct phone numbers', () => {
      expect(isValidPhone('+1234567890')).toBe(true);
      expect(isValidPhone('(*************')).toBe(true);
      expect(isValidPhone('************')).toBe(true);
      expect(isValidPhone('5551234567')).toBe(true);
    });

    it('should reject invalid phone numbers', () => {
      expect(isValidPhone('123')).toBe(false);
      expect(isValidPhone('abc-def-ghij')).toBe(false);
      expect(isValidPhone('')).toBe(false);
    });
  });

  describe('sanitizeString', () => {
    it('should remove HTML tags', () => {
      expect(sanitizeString('<script>alert("xss")</script>test')).toBe('alert("xss")test');
      expect(sanitizeString('Hello <b>world</b>!')).toBe('Hello world!');
    });

    it('should trim whitespace', () => {
      expect(sanitizeString('  test  ')).toBe('test');
      expect(sanitizeString('\n\ttest\n\t')).toBe('test');
    });

    it('should handle non-string input', () => {
      expect(sanitizeString(123)).toBe(123);
      expect(sanitizeString(null)).toBe(null);
      expect(sanitizeString(undefined)).toBe(undefined);
    });
  });

  describe('createSlug', () => {
    it('should create valid slugs', () => {
      expect(createSlug('Hello World')).toBe('hello-world');
      expect(createSlug('Test Company Inc.')).toBe('test-company-inc');
      expect(createSlug('Multiple   Spaces')).toBe('multiple-spaces');
    });

    it('should handle special characters', () => {
      expect(createSlug('Test & Company!')).toBe('test-company');
      expect(createSlug('Company@2023')).toBe('company2023');
    });

    it('should handle edge cases', () => {
      expect(createSlug('')).toBe('');
      expect(createSlug('   ')).toBe('');
      expect(createSlug('---')).toBe('');
    });
  });

  describe('removeEmptyValues', () => {
    it('should remove null and undefined values', () => {
      const input = {
        name: 'test',
        value: null,
        count: undefined,
        active: true,
        empty: ''
      };

      const result = removeEmptyValues(input);
      expect(result).toEqual({
        name: 'test',
        active: true
      });
    });

    it('should handle nested objects', () => {
      const input = {
        user: {
          name: 'test',
          email: null,
          profile: {
            age: 25,
            bio: ''
          }
        },
        settings: null
      };

      const result = removeEmptyValues(input);
      expect(result).toEqual({
        user: {
          name: 'test',
          profile: {
            age: 25
          }
        }
      });
    });

    it('should preserve arrays and zero values', () => {
      const input = {
        items: [],
        count: 0,
        active: false,
        name: null
      };

      const result = removeEmptyValues(input);
      expect(result).toEqual({
        items: [],
        count: 0,
        active: false
      });
    });
  });
});
