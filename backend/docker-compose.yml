# AI SDR Backend - Docker Compose Configuration
# For local development and testing

version: '3.8'

services:
  # Main Node.js Backend API
  api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - LOG_LEVEL=debug
      - CORS_ORIGIN=http://localhost:3000,http://localhost:3001
      - DYNAMODB_ENDPOINT=http://dynamodb:8000
      - LEAD_ENRICHMENT_SERVICE_URL=http://lead-enrichment:5000
    depends_on:
      - dynamodb
      - lead-enrichment
    volumes:
      - .:/app
      - /app/node_modules
    networks:
      - ai-sdr-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Python Lead Enrichment Service
  lead-enrichment:
    build:
      context: ./services/lead-enrichment
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - PORT=5000
      - DATABASE_PATH=/app/data/leads_enrichment.db
      - HUNTER_API_KEY=${HUNTER_API_KEY:-}
      - DEBUG=true
    volumes:
      - ./services/lead-enrichment/data:/app/data
      - ./services/lead-enrichment:/app
    networks:
      - ai-sdr-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Local DynamoDB for development
  dynamodb:
    image: amazon/dynamodb-local:latest
    ports:
      - "8000:8000"
    command: ["-jar", "DynamoDBLocal.jar", "-sharedDb", "-dbPath", "./data"]
    volumes:
      - dynamodb-data:/home/<USER>/data
    networks:
      - ai-sdr-network
    restart: unless-stopped

  # DynamoDB Admin UI
  dynamodb-admin:
    image: aaronshaf/dynamodb-admin:latest
    ports:
      - "8001:8001"
    environment:
      - DYNAMO_ENDPOINT=http://dynamodb:8000
      - AWS_REGION=us-east-1
      - AWS_ACCESS_KEY_ID=local
      - AWS_SECRET_ACCESS_KEY=local
    depends_on:
      - dynamodb
    networks:
      - ai-sdr-network
    restart: unless-stopped

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - ai-sdr-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - api
      - lead-enrichment
    networks:
      - ai-sdr-network
    restart: unless-stopped

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - ai-sdr-network
    restart: unless-stopped

  # Grafana for metrics visualization
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - ai-sdr-network
    restart: unless-stopped

volumes:
  dynamodb-data:
    driver: local
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

networks:
  ai-sdr-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
