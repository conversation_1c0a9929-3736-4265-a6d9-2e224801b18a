# Production Environment Configuration
# AI SDR Backend - Production Stage

# Service Configuration
service:
  name: ai-sdr-backend-prod
  stage: prod
  region: us-east-1

# Lambda Configuration
lambda:
  timeout: 30
  memorySize: 2048
  runtime: nodejs18.x
  reservedConcurrency: 100
  environment:
    NODE_ENV: production
    LOG_LEVEL: warn
    CORS_ORIGIN: "https://app.ai-sdr.com"

# DynamoDB Configuration
dynamodb:
  tables:
    leads:
      tableName: ai-sdr-leads-prod
      billingMode: PROVISIONED
      provisionedThroughput:
        readCapacityUnits: 10
        writeCapacityUnits: 10
      pointInTimeRecovery: true
      streamSpecification:
        streamViewType: NEW_AND_OLD_IMAGES
      globalSecondaryIndexes:
        - indexName: CompanyNameIndex
          keys:
            - companyName
          projection:
            type: ALL
          provisionedThroughput:
            readCapacityUnits: 5
            writeCapacityUnits: 5
    campaigns:
      tableName: ai-sdr-campaigns-prod
      billingMode: PROVISIONED
      provisionedThroughput:
        readCapacityUnits: 5
        writeCapacityUnits: 5
      pointInTimeRecovery: true
    users:
      tableName: ai-sdr-users-prod
      billingMode: PROVISIONED
      provisionedThroughput:
        readCapacityUnits: 5
        writeCapacityUnits: 5
      pointInTimeRecovery: true

# API Gateway Configuration
apiGateway:
  restApiId: ${env:PROD_API_GATEWAY_REST_API_ID, ''}
  restApiRootResourceId: ${env:PROD_API_GATEWAY_ROOT_RESOURCE_ID, ''}
  minimumCompressionSize: 1024
  binaryMediaTypes:
    - '*/*'
  requestValidators:
    validateRequestBody: true
    validateRequestParameters: true
  usagePlan:
    quota:
      limit: 10000
      period: DAY
    throttle:
      rateLimit: 100
      burstLimit: 200

# External Services Configuration
external:
  sendgrid:
    apiKey: ${env:PROD_SENDGRID_API_KEY}
    fromEmail: ${env:PROD_SENDGRID_FROM_EMAIL, '<EMAIL>'}
    fromName: ${env:PROD_SENDGRID_FROM_NAME, 'AI SDR'}
  
  twilio:
    accountSid: ${env:PROD_TWILIO_ACCOUNT_SID}
    authToken: ${env:PROD_TWILIO_AUTH_TOKEN}
    fromNumber: ${env:PROD_TWILIO_FROM_NUMBER}
  
  hubspot:
    apiKey: ${env:PROD_HUBSPOT_API_KEY}
    portalId: ${env:PROD_HUBSPOT_PORTAL_ID}
  
  openai:
    apiKey: ${env:PROD_OPENAI_API_KEY}
    model: ${env:PROD_OPENAI_MODEL, 'gpt-4'}
    maxTokens: ${env:PROD_OPENAI_MAX_TOKENS, '1000'}
  
  hunter:
    apiKey: ${env:PROD_HUNTER_API_KEY}

# Security Configuration
security:
  jwt:
    secret: ${env:PROD_JWT_SECRET}
    expiresIn: ${env:PROD_JWT_EXPIRES_IN, '8h'}
  
  cors:
    origin: 
      - https://app.ai-sdr.com
      - https://admin.ai-sdr.com
    credentials: true
    methods:
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
    headers:
      - Content-Type
      - Authorization
      - X-Requested-With

# Monitoring Configuration
monitoring:
  cloudwatch:
    enabled: true
    logRetentionInDays: 90
  
  xray:
    enabled: true
  
  alarms:
    enabled: true
    errorRate:
      threshold: 1
      period: 300
    latency:
      threshold: 3000
      period: 300
    throttles:
      threshold: 10
      period: 300

# VPC Configuration
vpc:
  enabled: true
  securityGroupIds:
    - ${env:PROD_SECURITY_GROUP_ID}
  subnetIds:
    - ${env:PROD_SUBNET_ID_1}
    - ${env:PROD_SUBNET_ID_2}

# Custom Configuration
custom:
  webpack:
    webpackConfig: 'webpack.config.js'
    includeModules: true
    packager: 'npm'
    excludeFiles: src/**/*.test.js
  
  alerts:
    stages:
      - prod
    topics:
      alarm: ${env:PROD_SNS_ALARM_TOPIC}
      ok: ${env:PROD_SNS_OK_TOPIC}
    alarms:
      - functionErrors
      - functionDuration
      - functionThrottles
      - functionInvocations
  
  warmup:
    enabled: true
    events:
      - schedule: rate(5 minutes)
    timeout: 20

# Plugins
plugins:
  - serverless-webpack
  - serverless-dotenv-plugin
  - serverless-plugin-aws-alerts
  - serverless-plugin-warmup

# Package Configuration
package:
  individually: true
  exclude:
    - .git/**
    - .gitignore
    - README.md
    - .env*
    - tests/**
    - coverage/**
    - docs/**
    - '*.md'
    - node_modules/aws-sdk/**
    - '**/*.test.js'
    - '**/*.spec.js'

# IAM Role Statements
iamRoleStatements:
  - Effect: Allow
    Action:
      - dynamodb:Query
      - dynamodb:Scan
      - dynamodb:GetItem
      - dynamodb:PutItem
      - dynamodb:UpdateItem
      - dynamodb:DeleteItem
    Resource:
      - "arn:aws:dynamodb:${self:provider.region}:*:table/${self:custom.dynamodb.tables.leads.tableName}"
      - "arn:aws:dynamodb:${self:provider.region}:*:table/${self:custom.dynamodb.tables.campaigns.tableName}"
      - "arn:aws:dynamodb:${self:provider.region}:*:table/${self:custom.dynamodb.tables.users.tableName}"
  
  - Effect: Allow
    Action:
      - ses:SendEmail
      - ses:SendRawEmail
    Resource: "*"
  
  - Effect: Allow
    Action:
      - ssm:GetParameter
      - ssm:GetParameters
      - ssm:GetParametersByPath
    Resource: "arn:aws:ssm:${self:provider.region}:*:parameter/ai-sdr/prod/*"
