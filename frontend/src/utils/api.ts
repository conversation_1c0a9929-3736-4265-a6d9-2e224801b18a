import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:3001';

export const fetchDashboardData = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/api/dashboard`);
    return response.data;
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    throw error;
  }
};

export const fetchLeads = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/api/leads`);
    return response.data;
  } catch (error) {
    console.error('Error fetching leads:', error);
    throw error;
  }
};

export const fetchLeadDetails = async (id: string) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/api/leads/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching lead details:', error);
    throw error;
  }
};

export const login = async (credentials: { username: string; password: string }) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/api/auth/login`, credentials);
    return response.data;
  } catch (error) {
    console.error('Error logging in:', error);
    throw error;
  }
};
