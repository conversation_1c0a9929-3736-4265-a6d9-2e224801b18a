{"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["cloudformation:*", "s3:*", "lambda:*", "apigateway:*", "iam:GetRole", "iam:PassRole", "iam:CreateRole", "iam:DeleteRole", "iam:AttachRolePolicy", "iam:DetachRolePolicy", "iam:PutRolePolicy", "iam:DeleteRolePolicy", "iam:GetRolePolicy", "iam:CreateServiceLinkedRole", "dynamodb:*", "sqs:*", "logs:*", "events:*", "sns:*", "ssm:GetParameter", "ssm:GetParameters", "ssm:PutParameter", "ssm:DeleteParameter", "kms:Decrypt", "kms:DescribeKey"], "Resource": "*"}, {"Effect": "Allow", "Action": ["iam:CreateRole", "iam:DeleteRole", "iam:GetRole", "iam:PassRole", "iam:AttachRolePolicy", "iam:DetachRolePolicy", "iam:PutRolePolicy", "iam:DeleteRolePolicy", "iam:GetRolePolicy", "iam:TagRole", "iam:UntagRole"], "Resource": ["arn:aws:iam::*:role/ai-sdr-backend-staging-*", "arn:aws:iam::*:role/*-lambdaRole"]}]}