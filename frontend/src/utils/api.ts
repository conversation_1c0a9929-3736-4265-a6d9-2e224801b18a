import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:3001';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor for auth token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('authToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Health check
export const checkHealth = async () => {
  try {
    const response = await api.get('/health');
    return response.data;
  } catch (error) {
    console.error('Error checking health:', error);
    throw error;
  }
};

// Auth API
export const login = async (credentials: { email: string; password: string }) => {
  try {
    const response = await api.post('/auth/login', credentials);
    if (response.data.token) {
      localStorage.setItem('authToken', response.data.token);
    }
    return response.data;
  } catch (error) {
    console.error('Error logging in:', error);
    throw error;
  }
};

export const register = async (userData: { email: string; password: string; name: string }) => {
  try {
    const response = await api.post('/auth/register', userData);
    return response.data;
  } catch (error) {
    console.error('Error registering:', error);
    throw error;
  }
};

export const logout = () => {
  localStorage.removeItem('authToken');
};

// Leads API
export const fetchLeads = async (params?: { page?: number; limit?: number; search?: string }) => {
  try {
    const response = await api.get('/leads', { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching leads:', error);
    throw error;
  }
};

export const fetchLeadDetails = async (id: string) => {
  try {
    const response = await api.get(`/leads/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching lead details:', error);
    throw error;
  }
};

export const createLead = async (leadData: any) => {
  try {
    const response = await api.post('/leads', leadData);
    return response.data;
  } catch (error) {
    console.error('Error creating lead:', error);
    throw error;
  }
};

export const updateLead = async (id: string, leadData: any) => {
  try {
    const response = await api.put(`/leads/${id}`, leadData);
    return response.data;
  } catch (error) {
    console.error('Error updating lead:', error);
    throw error;
  }
};

export const deleteLead = async (id: string) => {
  try {
    const response = await api.delete(`/leads/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error deleting lead:', error);
    throw error;
  }
};

// Analytics API
export const fetchDashboardData = async () => {
  try {
    const response = await api.get('/analytics/dashboard');
    return response.data;
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    throw error;
  }
};

export const fetchMetrics = async (params?: { startDate?: string; endDate?: string }) => {
  try {
    const response = await api.get('/analytics/metrics', { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching metrics:', error);
    throw error;
  }
};

// Outreach API
export const sendEmail = async (emailData: any) => {
  try {
    const response = await api.post('/outreach/email', emailData);
    return response.data;
  } catch (error) {
    console.error('Error sending email:', error);
    throw error;
  }
};

export const sendSMS = async (smsData: any) => {
  try {
    const response = await api.post('/outreach/sms', smsData);
    return response.data;
  } catch (error) {
    console.error('Error sending SMS:', error);
    throw error;
  }
};

export const executeCampaign = async (campaignData: any) => {
  try {
    const response = await api.post('/outreach/campaign', campaignData);
    return response.data;
  } catch (error) {
    console.error('Error executing campaign:', error);
    throw error;
  }
};

// Integrations API
export const connectCRM = async (crmData: any) => {
  try {
    const response = await api.post('/integrations/connect', crmData);
    return response.data;
  } catch (error) {
    console.error('Error connecting CRM:', error);
    throw error;
  }
};

export const syncCRM = async (crmType: string) => {
  try {
    const response = await api.post('/integrations/sync', { crmType });
    return response.data;
  } catch (error) {
    console.error('Error syncing CRM:', error);
    throw error;
  }
};

export default api;
