const Joi = require('joi');
const {
  lambdaWrapper,
  parseBody,
  getQueryParameters,
  successResponse,
  errorResponse,
  validateInput,
  ValidationError,
  logger
} = require('../shared/utils/api');
const { analyticsService } = require('../shared/utils/analytics');

// Validation schemas
const metricsQuerySchema = Joi.object({
  startDate: Joi.date().optional(),
  endDate: Joi.date().optional(),
  granularity: Joi.string().valid('hour', 'day', 'week', 'month').default('day'),
  metrics: Joi.array().items(Joi.string()).optional(),
  filters: Joi.object().optional()
});

const reportRequestSchema = Joi.object({
  reportType: Joi.string().valid('campaign', 'lead', 'performance', 'conversion').required(),
  startDate: Joi.date().optional(),
  endDate: Joi.date().optional(),
  filters: Joi.object().optional(),
  groupBy: Joi.array().items(Joi.string()).optional(),
  format: Joi.string().valid('json', 'csv', 'pdf').default('json')
});

const dashboardHandler = async (event) => {
  const queryParams = getQueryParameters(event);

  // Convert query parameters to proper types
  const queryData = {
    startDate: queryParams.startDate ? new Date(queryParams.startDate) : undefined,
    endDate: queryParams.endDate ? new Date(queryParams.endDate) : undefined,
    granularity: queryParams.granularity || 'day',
    filters: queryParams.filters ? JSON.parse(queryParams.filters) : {}
  };

  const validatedData = validateInput(queryData, metricsQuerySchema);

  logger.info('Fetching dashboard metrics', validatedData);

  try {
    const metrics = await analyticsService.getCampaignMetrics(validatedData);
    return successResponse(metrics);
  } catch (error) {
    logger.error('Failed to fetch dashboard metrics', { error: error.message });
    throw error;
  }
};

const reportHandler = async (event) => {
  const body = parseBody(event);
  const validatedData = validateInput(body, reportRequestSchema);

  logger.info('Generating analytics report', { reportType: validatedData.reportType });

  try {
    const report = await analyticsService.generateReport(validatedData);
    return successResponse(report);
  } catch (error) {
    logger.error('Failed to generate report', { error: error.message });
    throw error;
  }
};

const metricsHandler = async (event) => {
  const queryParams = getQueryParameters(event);

  const queryData = {
    startDate: queryParams.startDate ? new Date(queryParams.startDate) : undefined,
    endDate: queryParams.endDate ? new Date(queryParams.endDate) : undefined,
    granularity: queryParams.granularity || 'day',
    metrics: queryParams.metrics ? queryParams.metrics.split(',') : undefined,
    filters: queryParams.filters ? JSON.parse(queryParams.filters) : {}
  };

  const validatedData = validateInput(queryData, metricsQuerySchema);

  logger.info('Fetching specific metrics', validatedData);

  try {
    const metrics = await analyticsService.getCampaignMetrics(validatedData);
    return successResponse(metrics);
  } catch (error) {
    logger.error('Failed to fetch metrics', { error: error.message });
    throw error;
  }
};

const publishMetricHandler = async (event) => {
  const body = parseBody(event);

  const metricSchema = Joi.object({
    metricName: Joi.string().required(),
    value: Joi.number().required(),
    dimensions: Joi.object().optional().default({})
  });

  const validatedData = validateInput(body, metricSchema);

  logger.info('Publishing metric', validatedData);

  try {
    await analyticsService.publishMetric(
      validatedData.metricName,
      validatedData.value,
      validatedData.dimensions
    );

    return successResponse({
      message: 'Metric published successfully',
      metricName: validatedData.metricName,
      value: validatedData.value
    });
  } catch (error) {
    logger.error('Failed to publish metric', { error: error.message });
    throw error;
  }
};

// Main handler for API Gateway routing
const main = lambdaWrapper(async (event, context) => {
  const { httpMethod, path } = event;
  const pathSegments = path.split('/').filter(Boolean);
  const action = pathSegments[pathSegments.length - 1];

  logger.info('Analytics service request', { httpMethod, path, action });

  switch (httpMethod) {
    case 'GET':
      switch (action) {
        case 'dashboard':
          return await dashboardHandler(event, context);
        case 'metrics':
          return await metricsHandler(event, context);
        default:
          throw new ValidationError(`Unknown action: ${action}`);
      }
    case 'POST':
      switch (action) {
        case 'report':
        case 'reports':
          return await reportHandler(event, context);
        case 'metric':
        case 'metrics':
          return await publishMetricHandler(event, context);
        default:
          throw new ValidationError(`Unknown action: ${action}`);
      }
    case 'OPTIONS':
      return successResponse({ message: 'CORS preflight' });
    default:
      throw new ValidationError(`Unsupported HTTP method: ${httpMethod}`);
  }
});

// Export wrapped handlers
module.exports = {
  main,
  dashboardHandler: lambdaWrapper(dashboardHandler),
  reportHandler: lambdaWrapper(reportHandler),
  metricsHandler: lambdaWrapper(metricsHandler),
  publishMetricHandler: lambdaWrapper(publishMetricHandler)
};