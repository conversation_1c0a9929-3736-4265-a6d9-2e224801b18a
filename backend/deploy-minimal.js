/**
 * Minimal deployment script for AI SDR backend
 * This creates a simple Express server that can be deployed to various platforms
 */

const express = require('express');
const cors = require('cors');
const { AIService } = require('./services/shared/utils/aiService');

const app = express();
const port = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Initialize AI Service
const aiService = new AIService();

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'AI SDR Backend',
    timestamp: new Date().toISOString(),
    geminiAvailable: aiService.isAvailable(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// AI-powered email generation endpoint
app.post('/api/generate-email', async (req, res) => {
  try {
    const { lead, campaign } = req.body;
    
    if (!lead || !campaign) {
      return res.status(400).json({
        error: 'Missing required fields: lead and campaign'
      });
    }

    const result = await aiService.generatePersonalizedEmail(lead, campaign);
    
    res.json({
      success: true,
      email: result,
      generatedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Email generation error:', error);
    res.status(500).json({
      error: 'Failed to generate email',
      message: error.message
    });
  }
});

// AI-powered lead scoring endpoint
app.post('/api/score-lead', async (req, res) => {
  try {
    const { lead } = req.body;
    
    if (!lead) {
      return res.status(400).json({
        error: 'Missing required field: lead'
      });
    }

    const result = await aiService.calculateLeadScore(lead);
    
    res.json({
      success: true,
      score: result,
      scoredAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Lead scoring error:', error);
    res.status(500).json({
      error: 'Failed to score lead',
      message: error.message
    });
  }
});

// Campaign strategy generation endpoint
app.post('/api/generate-strategy', async (req, res) => {
  try {
    const { leads, campaignGoals } = req.body;
    
    if (!leads || !campaignGoals) {
      return res.status(400).json({
        error: 'Missing required fields: leads and campaignGoals'
      });
    }

    const result = await aiService.generateCampaignStrategy(leads, campaignGoals);
    
    res.json({
      success: true,
      strategy: result,
      generatedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Strategy generation error:', error);
    res.status(500).json({
      error: 'Failed to generate strategy',
      message: error.message
    });
  }
});

// API documentation endpoint
app.get('/api/docs', (req, res) => {
  res.json({
    title: 'AI SDR Backend API',
    version: '1.0.0',
    description: 'AI-powered Sales Development Representative backend with Google Gemini integration',
    endpoints: {
      'GET /health': 'Health check and service status',
      'POST /api/generate-email': 'Generate personalized emails using AI',
      'POST /api/score-lead': 'Calculate AI-powered lead scores',
      'POST /api/generate-strategy': 'Generate campaign strategies'
    },
    geminiIntegration: aiService.isAvailable()
  });
});

// Start server
if (require.main === module) {
  app.listen(port, () => {
    console.log(`🚀 AI SDR Backend running on port ${port}`);
    console.log(`📚 API Documentation: http://localhost:${port}/api/docs`);
    console.log(`❤️  Health Check: http://localhost:${port}/health`);
    console.log(`🤖 Gemini AI: ${aiService.isAvailable() ? 'Available' : 'Not configured'}`);
  });
}

module.exports = app;
