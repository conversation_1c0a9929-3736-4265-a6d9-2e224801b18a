const TestHelpers = require('../../../shared/utils/testHelpers');

// Setup test environment before importing modules
TestHelpers.setupTestEnvironment();

const { 
  importLeads, 
  getLeads, 
  getLeadById, 
  updateLead, 
  deleteLead, 
  createLead 
} = require('../../handler');

describe('Leads Handler', () => {
  let mockLeadService;

  beforeEach(() => {
    TestHelpers.setupTestEnvironment();
    
    // Mock leadService
    mockLeadService = {
      createLead: jest.fn(),
      getLead: jest.fn(),
      getLeads: jest.fn(),
      updateLead: jest.fn(),
      deleteLead: jest.fn()
    };
    
    // Replace the actual service with mock
    jest.doMock('../../../shared/models/lead', () => ({
      leadService: mockLeadService
    }));
  });

  afterEach(() => {
    TestHelpers.cleanupTestEnvironment();
  });

  describe('createLead', () => {
    it('should successfully create a new lead', async () => {
      const testLead = TestHelpers.generateTestLead();
      mockLeadService.createLead.mockResolvedValue(testLead);

      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'POST',
        body: JSON.stringify({
          companyName: 'Test Company',
          contactEmail: '<EMAIL>',
          contactName: 'Test Contact',
          revenue: 1000000,
          industry: 'Technology'
        })
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await createLead(event, context);

      const body = TestHelpers.assertValidResponse(response, 201);
      expect(body.data.companyName).toBe('Test Company');
      expect(mockLeadService.createLead).toHaveBeenCalled();
    });

    it('should fail to create lead with invalid email', async () => {
      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'POST',
        body: JSON.stringify({
          companyName: 'Test Company',
          contactEmail: 'invalid-email',
          contactName: 'Test Contact'
        })
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await createLead(event, context);

      const body = TestHelpers.assertValidResponse(response, 400);
      expect(body.error.message).toBe('Validation failed');
    });

    it('should fail to create lead with missing required fields', async () => {
      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'POST',
        body: JSON.stringify({
          contactEmail: '<EMAIL>'
          // missing companyName
        })
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await createLead(event, context);

      const body = TestHelpers.assertValidResponse(response, 400);
      expect(body.error.message).toBe('Validation failed');
    });
  });

  describe('getLeads', () => {
    it('should successfully retrieve leads with default pagination', async () => {
      const testLeads = [
        TestHelpers.generateTestLead(),
        TestHelpers.generateTestLead()
      ];

      const mockResult = {
        leads: testLeads,
        pagination: {
          total: 2,
          page: 1,
          limit: 20,
          totalPages: 1,
          hasNext: false,
          hasPrev: false
        }
      };

      mockLeadService.getLeads.mockResolvedValue(mockResult);

      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'GET'
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await getLeads(event, context);

      const body = TestHelpers.assertValidResponse(response, 200);
      expect(body.data.leads).toHaveLength(2);
      expect(body.data.pagination.total).toBe(2);
      expect(mockLeadService.getLeads).toHaveBeenCalledWith({
        page: 1,
        limit: 20,
        sort: 'createdAt',
        order: 'desc',
        filter: {}
      });
    });

    it('should retrieve leads with custom pagination and filters', async () => {
      const mockResult = {
        leads: [],
        pagination: {
          total: 0,
          page: 2,
          limit: 10,
          totalPages: 0,
          hasNext: false,
          hasPrev: true
        }
      };

      mockLeadService.getLeads.mockResolvedValue(mockResult);

      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'GET',
        queryStringParameters: {
          page: '2',
          limit: '10',
          status: 'sent',
          industry: 'Technology',
          search: 'test'
        }
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await getLeads(event, context);

      const body = TestHelpers.assertValidResponse(response, 200);
      expect(mockLeadService.getLeads).toHaveBeenCalledWith({
        page: 2,
        limit: 10,
        sort: 'createdAt',
        order: 'desc',
        filter: {
          emailStatus: 'sent',
          industry: 'Technology',
          companyName: '*test*'
        }
      });
    });
  });

  describe('getLeadById', () => {
    it('should successfully retrieve a lead by ID', async () => {
      const testLead = TestHelpers.generateTestLead();
      mockLeadService.getLead.mockResolvedValue(testLead);

      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'GET',
        pathParameters: { id: testLead.leadId }
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await getLeadById(event, context);

      const body = TestHelpers.assertValidResponse(response, 200);
      expect(body.data.leadId).toBe(testLead.leadId);
      expect(mockLeadService.getLead).toHaveBeenCalledWith(testLead.leadId);
    });

    it('should fail when lead ID is missing', async () => {
      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'GET',
        pathParameters: null
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await getLeadById(event, context);

      const body = TestHelpers.assertValidResponse(response, 400);
      expect(body.error.message).toBe('Lead ID is required');
    });

    it('should handle lead not found error', async () => {
      const { NotFoundError } = require('../../../shared/utils/api');
      mockLeadService.getLead.mockRejectedValue(new NotFoundError('Lead not found'));

      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'GET',
        pathParameters: { id: 'nonexistent-id' }
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await getLeadById(event, context);

      const body = TestHelpers.assertValidResponse(response, 404);
      expect(body.error.message).toBe('Lead not found');
    });
  });

  describe('updateLead', () => {
    it('should successfully update a lead', async () => {
      const testLead = TestHelpers.generateTestLead();
      const updatedLead = { ...testLead, companyName: 'Updated Company' };
      
      mockLeadService.updateLead.mockResolvedValue(updatedLead);

      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'PUT',
        pathParameters: { id: testLead.leadId },
        body: JSON.stringify({
          companyName: 'Updated Company',
          score: 85
        })
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await updateLead(event, context);

      const body = TestHelpers.assertValidResponse(response, 200);
      expect(body.data.companyName).toBe('Updated Company');
      expect(mockLeadService.updateLead).toHaveBeenCalledWith(
        testLead.leadId,
        { companyName: 'Updated Company', score: 85 }
      );
    });

    it('should fail update with invalid data', async () => {
      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'PUT',
        pathParameters: { id: 'test-id' },
        body: JSON.stringify({
          contactEmail: 'invalid-email',
          score: 150 // Invalid score > 100
        })
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await updateLead(event, context);

      const body = TestHelpers.assertValidResponse(response, 400);
      expect(body.error.message).toBe('Validation failed');
    });
  });

  describe('deleteLead', () => {
    it('should successfully delete a lead', async () => {
      mockLeadService.deleteLead.mockResolvedValue();

      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'DELETE',
        pathParameters: { id: 'test-lead-id' }
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await deleteLead(event, context);

      const body = TestHelpers.assertValidResponse(response, 204);
      expect(body.data.message).toBe('Lead deleted successfully');
      expect(mockLeadService.deleteLead).toHaveBeenCalledWith('test-lead-id');
    });

    it('should fail when lead ID is missing', async () => {
      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'DELETE',
        pathParameters: null
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await deleteLead(event, context);

      const body = TestHelpers.assertValidResponse(response, 400);
      expect(body.error.message).toBe('Lead ID is required');
    });
  });

  describe('importLeads', () => {
    it('should successfully import multiple leads', async () => {
      const testLeads = [
        TestHelpers.generateTestLead(),
        TestHelpers.generateTestLead()
      ];

      mockLeadService.createLead
        .mockResolvedValueOnce(testLeads[0])
        .mockResolvedValueOnce(testLeads[1]);

      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'POST',
        body: JSON.stringify({
          leads: [
            {
              companyName: 'Company 1',
              contactEmail: '<EMAIL>'
            },
            {
              companyName: 'Company 2',
              contactEmail: '<EMAIL>'
            }
          ]
        })
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await importLeads(event, context);

      const body = TestHelpers.assertValidResponse(response, 200);
      expect(body.data.imported).toBe(2);
      expect(body.data.errors).toBe(0);
      expect(body.data.leads).toHaveLength(2);
      expect(mockLeadService.createLead).toHaveBeenCalledTimes(2);
    });

    it('should handle partial import failures', async () => {
      const testLead = TestHelpers.generateTestLead();
      
      mockLeadService.createLead
        .mockResolvedValueOnce(testLead)
        .mockRejectedValueOnce(new Error('Duplicate email'));

      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'POST',
        body: JSON.stringify({
          leads: [
            {
              companyName: 'Company 1',
              contactEmail: '<EMAIL>'
            },
            {
              companyName: 'Company 2',
              contactEmail: 'invalid-email'
            }
          ]
        })
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await importLeads(event, context);

      const body = TestHelpers.assertValidResponse(response, 200);
      expect(body.data.imported).toBe(1);
      expect(body.data.errors).toBe(1);
      expect(body.data.importErrors).toHaveLength(1);
    });

    it('should fail with empty leads array', async () => {
      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'POST',
        body: JSON.stringify({
          leads: []
        })
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await importLeads(event, context);

      const body = TestHelpers.assertValidResponse(response, 400);
      expect(body.error.message).toBe('Validation failed');
    });
  });
});
