# AI SDR Backend - Deployment Guide

This guide covers deployment strategies for the AI SDR Backend across different environments.

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- AWS CLI configured
- Serverless Framework
- Docker (for local development)
- Python 3.11+ (for lead enrichment service)

### Environment Setup

1. **<PERSON>lone and Install Dependencies**
```bash
git clone https://github.com/GEMDevEng/AI_SDR.git
cd AI_SDR/backend
npm install
```

2. **Configure Environment Variables**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Start Local Development**
```bash
# Using Docker Compose (recommended)
docker-compose up -d

# Or manually
npm run dev
```

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Gateway   │    │   Lambda        │
│   (React)       │◄──►│   (AWS)         │◄──►│   Functions     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────┐             │
                       │   DynamoDB      │◄────────────┘
                       │   (AWS)         │
                       └─────────────────┘
                                │
                       ┌─────────────────┐
                       │   Python        │
                       │   Enrichment    │
                       │   Service       │
                       └─────────────────┘
```

## 🌍 Environments

### Development
- **Purpose**: Local development and testing
- **Infrastructure**: Docker Compose
- **Database**: DynamoDB Local
- **Monitoring**: Basic logging

### Staging
- **Purpose**: Pre-production testing
- **Infrastructure**: AWS Lambda + API Gateway
- **Database**: DynamoDB (PAY_PER_REQUEST)
- **Monitoring**: CloudWatch + X-Ray

### Production
- **Purpose**: Live application
- **Infrastructure**: AWS Lambda + API Gateway + VPC
- **Database**: DynamoDB (PROVISIONED)
- **Monitoring**: CloudWatch + X-Ray + Alarms

## 📦 Deployment Methods

### 1. Serverless Framework (Recommended)

```bash
# Deploy to staging
./deploy.sh staging

# Deploy to production
./deploy.sh prod

# Deploy with custom configuration
serverless deploy --stage prod --config config/prod.yml
```

### 2. Docker Deployment

```bash
# Build and run locally
docker-compose up --build

# Production deployment
docker build -t ai-sdr-backend .
docker run -p 3001:3001 ai-sdr-backend
```

### 3. Manual AWS Deployment

```bash
# Package application
npm run build

# Deploy using AWS CLI
aws lambda update-function-code \
  --function-name ai-sdr-backend-prod \
  --zip-file fileb://deployment.zip
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `NODE_ENV` | Environment mode | Yes | development |
| `AWS_REGION` | AWS region | Yes | us-east-1 |
| `SENDGRID_API_KEY` | SendGrid API key | Yes | - |
| `OPENAI_API_KEY` | OpenAI API key | No | - |
| `JWT_SECRET` | JWT signing secret | Yes | - |
| `CORS_ORIGIN` | Allowed CORS origins | Yes | * |

### Service Configuration

Each environment has its own configuration file:
- `config/dev.yml` - Development
- `config/staging.yml` - Staging  
- `config/prod.yml` - Production

## 🔍 Monitoring & Observability

### CloudWatch Metrics
- Function duration
- Error rates
- Invocation counts
- Memory utilization

### Custom Metrics
- Lead processing rate
- Email delivery success
- AI service response times
- Database query performance

### Alarms
- Error rate > 1% (Production)
- Function duration > 30s
- Memory utilization > 80%
- DynamoDB throttling

### Dashboards
- Grafana dashboards for local development
- CloudWatch dashboards for AWS environments

## 🚨 Troubleshooting

### Common Issues

1. **Cold Start Latency**
   - Solution: Enable function warming in production
   - Configuration: `custom.warmup.enabled: true`

2. **DynamoDB Throttling**
   - Solution: Increase provisioned capacity
   - Monitor: CloudWatch DynamoDB metrics

3. **Memory Issues**
   - Solution: Increase Lambda memory allocation
   - Monitor: CloudWatch memory utilization

4. **API Gateway Timeouts**
   - Solution: Optimize function performance
   - Increase timeout limits if necessary

### Debug Commands

```bash
# Check deployment status
serverless info --stage prod

# View logs
serverless logs -f leads --stage prod --tail

# Run health checks
./deploy.sh health prod

# Test endpoints
curl -X GET https://api.ai-sdr.com/health
```

## 🔄 CI/CD Pipeline

### GitHub Actions Workflow

1. **Test Stage**
   - Run unit tests
   - Security scanning
   - Code quality checks

2. **Build Stage**
   - Install dependencies
   - Build application
   - Create deployment package

3. **Deploy Stage**
   - Deploy to staging (on main branch)
   - Deploy to production (on release branch)
   - Run integration tests

### Manual Deployment

```bash
# Quick deployment to staging
git push origin main

# Production deployment
git checkout -b release/v1.0.0
git push origin release/v1.0.0
```

## 🔐 Security

### Best Practices
- Use IAM roles with minimal permissions
- Enable VPC for production
- Encrypt environment variables
- Regular security audits

### Secrets Management
- Use AWS Systems Manager Parameter Store
- Rotate secrets regularly
- Never commit secrets to version control

## 📊 Performance Optimization

### Lambda Optimization
- Right-size memory allocation
- Use connection pooling
- Implement caching strategies
- Optimize cold starts

### Database Optimization
- Use appropriate indexes
- Implement query optimization
- Monitor read/write capacity
- Use DynamoDB best practices

## 🆘 Disaster Recovery

### Backup Strategy
- DynamoDB point-in-time recovery enabled
- Regular configuration backups
- Code repository backups

### Rollback Procedures
```bash
# Rollback to previous version
./deploy.sh rollback prod

# Manual rollback
serverless rollback --timestamp <timestamp> --stage prod
```

## 📞 Support

For deployment issues:
1. Check the troubleshooting section
2. Review CloudWatch logs
3. Contact the development team
4. Create an issue in the repository

## 📚 Additional Resources

- [Serverless Framework Documentation](https://www.serverless.com/framework/docs/)
- [AWS Lambda Best Practices](https://docs.aws.amazon.com/lambda/latest/dg/best-practices.html)
- [DynamoDB Best Practices](https://docs.aws.amazon.com/amazondynamodb/latest/developerguide/best-practices.html)
