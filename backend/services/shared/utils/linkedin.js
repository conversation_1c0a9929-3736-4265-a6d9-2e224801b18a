const linkedin = require('linkedin-api');

module.exports.sendLinkedInMessage = async (profileUrl, message) => {
  const client = new linkedin({
    clientId: process.env.LINKEDIN_CLIENT_ID,
    clientSecret: process.env.LINKEDIN_CLIENT_SECRET,
    redirectUri: process.env.LINKEDIN_REDIRECT_URI
  });

  try {
    await client.message.send({
      profileUrl,
      message
    });
    return { success: true };
  } catch (error) {
    console.error('LinkedIn message error:', error);
    throw error;
  }
};