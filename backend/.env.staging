# Staging Environment Variables
# DO NOT COMMIT THIS FILE TO VERSION CONTROL

# Application Environment
NODE_ENV=staging
STAGE=staging

# JWT Configuration
STAGING_JWT_SECRET=87d86aa764c9eab1e466cd751f2bff6f3be2c4c6fe8626b411ea3930db7746e3cc73a51af8875631f4b46824196e6f70699ead68963a4f573f560ed46763aa00
JWT_EXPIRES_IN=24h

# AWS Configuration (already configured via serverless)
AWS_REGION=us-east-1

# Database Configuration
DYNAMODB_TABLE_PREFIX=ai-sdr-staging

# AI Services - Google Gemini
STAGING_GEMINI_API_KEY=AIzaSyC_TnBONRbqwO4_Ju7P4joW1JNArYLjrhg
STAGING_GEMINI_MODEL=gemini-pro
STAGING_GEMINI_MAX_TOKENS=800

# Communication Services
STAGING_SENDGRID_API_KEY=*********************************************************************
STAGING_SENDGRID_FROM_EMAIL=<EMAIL>
STAGING_SENDGRID_FROM_NAME=AI SDR Staging

# Data Enrichment Services
# STAGING_HUNTER_API_KEY=your_hunter_api_key_here

# CRM Integrations
# STAGING_HUBSPOT_API_KEY=your_hubspot_api_key_here
# STAGING_HUBSPOT_PORTAL_ID=your_hubspot_portal_id_here

# Security
CORS_ORIGIN=https://staging.ai-sdr.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Monitoring and Logging
LOG_LEVEL=info
CLOUDWATCH_LOG_GROUP=/aws/lambda/ai-sdr-staging

# Feature Flags
ENABLE_SMS_OUTREACH=false
ENABLE_LINKEDIN_OUTREACH=false
ENABLE_ADVANCED_ANALYTICS=true
