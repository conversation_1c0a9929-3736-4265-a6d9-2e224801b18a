const twilio = require('twilio');
const Joi = require('joi');
const { logger, ValidationError, InternalServerError } = require('./api');
const { configService } = require('./config');
const { retryWithBackoff, isValidPhone } = require('./helpers');

// SMS validation schema
const smsSchema = Joi.object({
  to: Joi.string().required().custom((value, helpers) => {
    if (!isValidPhone(value)) {
      return helpers.error('any.invalid');
    }
    return value;
  }, 'phone validation'),
  message: Joi.string().required().min(1).max(1600), // SMS character limit
  from: Joi.string().optional(),
  mediaUrl: Joi.array().items(Joi.string().uri()).optional(),
  statusCallback: Joi.string().uri().optional(),
  maxPrice: Joi.number().positive().optional(),
  provideFeedback: Joi.boolean().optional(),
  validityPeriod: Joi.number().min(1).max(14400).optional() // Max 4 hours in seconds
});

class SmsService {
  constructor() {
    this.initialized = false;
    this.client = null;
    this.config = null;
  }

  /**
   * Initialize the SMS service with configuration
   */
  async initialize() {
    if (this.initialized) {
      return;
    }

    try {
      this.config = await configService.getSmsConfig();
      this.client = twilio(this.config.twilioAccountSid, this.config.twilioAuthToken);
      this.initialized = true;

      logger.info('SMS service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize SMS service', { error: error.message });
      throw new InternalServerError('SMS service initialization failed');
    }
  }

  /**
   * Send SMS with validation and retry logic
   * @param {Object} smsData - SMS data
   * @returns {Promise<Object>} Send result
   */
  async sendSMS(smsData) {
    await this.initialize();

    // Validate input
    const { error, value } = smsSchema.validate(smsData);
    if (error) {
      throw new ValidationError('Invalid SMS data', error.details);
    }

    const {
      to,
      message,
      from,
      mediaUrl,
      statusCallback,
      maxPrice,
      provideFeedback,
      validityPeriod
    } = value;

    // Build SMS message
    const messageData = {
      body: message,
      from: from || this.config.twilioPhoneNumber,
      to
    };

    // Add optional fields
    if (mediaUrl && mediaUrl.length > 0) messageData.mediaUrl = mediaUrl;
    if (statusCallback) messageData.statusCallback = statusCallback;
    if (maxPrice) messageData.maxPrice = maxPrice.toString();
    if (provideFeedback !== undefined) messageData.provideFeedback = provideFeedback;
    if (validityPeriod) messageData.validityPeriod = validityPeriod;

    try {
      logger.info('Sending SMS', { to, messageLength: message.length });

      // Send with retry logic
      const result = await retryWithBackoff(
        () => this.client.messages.create(messageData),
        3, // max retries
        1000 // base delay
      );

      logger.info('SMS sent successfully', {
        sid: result.sid,
        to,
        status: result.status
      });

      return {
        success: true,
        sid: result.sid,
        status: result.status,
        to,
        from: result.from,
        dateCreated: result.dateCreated,
        price: result.price,
        priceUnit: result.priceUnit
      };
    } catch (error) {
      logger.error('Failed to send SMS', {
        error: error.message,
        code: error.code,
        to,
        messageLength: message.length
      });

      // Handle specific Twilio errors
      if (error.code === 20003) {
        throw new ValidationError('Authentication failed - invalid Twilio credentials');
      } else if (error.code === 21211) {
        throw new ValidationError('Invalid phone number format');
      } else if (error.code === 21408) {
        throw new ValidationError('Permission denied for this phone number');
      } else if (error.code === 21610) {
        throw new ValidationError('Message cannot be sent to this number');
      } else if (error.status >= 400 && error.status < 500) {
        throw new ValidationError(`SMS service error: ${error.message}`);
      }

      throw new InternalServerError('Failed to send SMS');
    }
  }

  /**
   * Send bulk SMS messages
   * @param {Array} messages - Array of SMS data objects
   * @returns {Promise<Object>} Bulk send result
   */
  async sendBulkSMS(messages) {
    await this.initialize();

    if (!Array.isArray(messages) || messages.length === 0) {
      throw new ValidationError('Messages array is required and must not be empty');
    }

    logger.info('Sending bulk SMS', { count: messages.length });

    const results = {
      sent: [],
      failed: []
    };

    // Process messages in batches to avoid rate limits
    const batchSize = 5; // Conservative batch size for SMS
    for (let i = 0; i < messages.length; i += batchSize) {
      const batch = messages.slice(i, i + batchSize);

      const batchPromises = batch.map(async (smsData, index) => {
        try {
          const result = await this.sendSMS(smsData);
          results.sent.push({ index: i + index, result });
        } catch (error) {
          results.failed.push({
            index: i + index,
            sms: smsData,
            error: error.message
          });
        }
      });

      await Promise.all(batchPromises);

      // Add delay between batches to respect rate limits
      if (i + batchSize < messages.length) {
        await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
      }
    }

    logger.info('Bulk SMS send completed', {
      total: messages.length,
      sent: results.sent.length,
      failed: results.failed.length
    });

    return results;
  }

  /**
   * Get message status
   * @param {string} messageSid - Message SID
   * @returns {Promise<Object>} Message status
   */
  async getMessageStatus(messageSid) {
    await this.initialize();

    try {
      const message = await this.client.messages(messageSid).fetch();

      return {
        sid: message.sid,
        status: message.status,
        to: message.to,
        from: message.from,
        body: message.body,
        dateCreated: message.dateCreated,
        dateSent: message.dateSent,
        dateUpdated: message.dateUpdated,
        price: message.price,
        priceUnit: message.priceUnit,
        errorCode: message.errorCode,
        errorMessage: message.errorMessage
      };
    } catch (error) {
      logger.error('Failed to get message status', {
        messageSid,
        error: error.message
      });
      throw new InternalServerError('Failed to get message status');
    }
  }

  /**
   * Validate phone number
   * @param {string} phoneNumber - Phone number to validate
   * @returns {Promise<Object>} Validation result
   */
  async validatePhoneNumber(phoneNumber) {
    await this.initialize();

    try {
      const lookup = await this.client.lookups.v1.phoneNumbers(phoneNumber).fetch();

      return {
        valid: true,
        phoneNumber: lookup.phoneNumber,
        nationalFormat: lookup.nationalFormat,
        countryCode: lookup.countryCode,
        carrier: lookup.carrier
      };
    } catch (error) {
      if (error.code === 20404) {
        return {
          valid: false,
          phoneNumber,
          error: 'Invalid phone number'
        };
      }

      logger.error('Phone number validation failed', {
        phoneNumber,
        error: error.message
      });
      throw new InternalServerError('Phone number validation failed');
    }
  }
}

// Export singleton instance
const smsService = new SmsService();

module.exports = {
  SmsService,
  smsService,
  // Legacy export for backward compatibility
  sendSMS: (to, message) => smsService.sendSMS({ to, message })
};