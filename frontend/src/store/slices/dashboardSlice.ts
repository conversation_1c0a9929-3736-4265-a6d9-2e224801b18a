import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { fetchDashboardData as apiFetchDashboardData, fetchMetrics as apiFetchMetrics } from '../../utils/api';

interface DashboardMetrics {
  totalLeads: number;
  qualifiedLeads: number;
  convertedLeads: number;
  conversionRate: number;
  emailsSent: number;
  emailOpenRate: number;
  emailClickRate: number;
  smsSent: number;
  smsResponseRate: number;
  revenueGenerated: number;
  averageDealSize: number;
  activeCampaigns: number;
}

interface ChartData {
  name: string;
  value: number;
  date?: string;
}

interface DashboardState {
  metrics: DashboardMetrics | null;
  leadsChart: ChartData[];
  conversionChart: ChartData[];
  revenueChart: ChartData[];
  campaignPerformance: ChartData[];
  loading: boolean;
  error: string | null;
  lastUpdated: string | null;
}

const initialState: DashboardState = {
  metrics: null,
  leadsChart: [],
  conversionChart: [],
  revenueChart: [],
  campaignPerformance: [],
  loading: false,
  error: null,
  lastUpdated: null,
};

// Async thunks
export const fetchDashboardData = createAsyncThunk(
  'dashboard/fetchDashboardData',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiFetchDashboardData();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch dashboard data');
    }
  }
);

export const fetchMetrics = createAsyncThunk(
  'dashboard/fetchMetrics',
  async (params?: { startDate?: string; endDate?: string }, { rejectWithValue }) => {
    try {
      const response = await apiFetchMetrics(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch metrics');
    }
  }
);

const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateLastRefresh: (state) => {
      state.lastUpdated = new Date().toISOString();
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch dashboard data
      .addCase(fetchDashboardData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchDashboardData.fulfilled, (state, action) => {
        state.loading = false;
        state.metrics = action.payload.metrics;
        state.leadsChart = action.payload.leadsChart || [];
        state.conversionChart = action.payload.conversionChart || [];
        state.revenueChart = action.payload.revenueChart || [];
        state.campaignPerformance = action.payload.campaignPerformance || [];
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(fetchDashboardData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Fetch metrics
      .addCase(fetchMetrics.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchMetrics.fulfilled, (state, action) => {
        state.loading = false;
        state.metrics = { ...state.metrics, ...action.payload };
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(fetchMetrics.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, updateLastRefresh } = dashboardSlice.actions;
export default dashboardSlice.reducer;
