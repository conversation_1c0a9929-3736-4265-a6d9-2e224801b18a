/**
 * Simple integration test for Google Gemini API migration
 * This script tests the AIService with mock data to verify the migration
 */

const { AIService } = require('./services/shared/utils/aiService');

// Mock environment variables for testing
process.env.GEMINI_API_KEY = 'test-key-for-integration-test';
process.env.GEMINI_MODEL = 'gemini-pro';

async function testGeminiIntegration() {
  console.log('🧪 Testing Google Gemini API Integration...\n');

  try {
    // Initialize AI Service
    const aiService = new AIService();
    
    console.log('✅ AIService Configuration:');
    console.log(`   - API Key: ${aiService.geminiApiKey ? 'Configured' : 'Missing'}`);
    console.log(`   - Base URL: ${aiService.geminiBaseUrl}`);
    console.log(`   - Model: ${aiService.model}`);
    console.log(`   - Available: ${aiService.isAvailable()}\n`);

    // Test 1: Check service availability
    console.log('🔍 Test 1: Service Availability');
    if (aiService.isAvailable()) {
      console.log('✅ AI Service is available\n');
    } else {
      console.log('❌ AI Service is not available\n');
      return;
    }

    // Test 2: Mock API call structure for email generation
    console.log('🔍 Test 2: Email Generation API Call Structure');
    const mockLead = {
      name: 'John Doe',
      company: 'Mining Corp',
      email: '<EMAIL>',
      title: 'Operations Manager'
    };

    const mockCampaign = {
      name: 'Q1 Mining Equipment Campaign',
      objective: 'Generate qualified leads for mining equipment'
    };

    // Mock the axios client to capture the API call
    const originalPost = aiService.client.post;
    let capturedApiCall = null;

    aiService.client.post = async (url, data) => {
      capturedApiCall = { url, data };
      // Return mock Gemini response structure
      return {
        data: {
          candidates: [{
            content: {
              parts: [{
                text: 'Subject: Advanced Mining Solutions for Mining Corp\n\nDear John,\n\nI hope this email finds you well. As Operations Manager at Mining Corp, you understand the importance of efficient mining operations...\n\nBest regards,\nAI SDR Agent'
              }]
            }
          }]
        }
      };
    };

    try {
      const result = await aiService.generatePersonalizedEmail(mockLead, mockCampaign);
      
      console.log('✅ Email generation completed successfully');
      console.log('📧 Generated email preview:', result?.subject || 'Email generated');
      
      if (capturedApiCall) {
        console.log('🔗 API Call Details:');
        console.log(`   - URL: ${capturedApiCall.url}`);
        console.log(`   - Uses Gemini format: ${capturedApiCall.data.contents ? '✅' : '❌'}`);
        console.log(`   - Has generation config: ${capturedApiCall.data.generationConfig ? '✅' : '❌'}`);
        console.log(`   - Temperature: ${capturedApiCall.data.generationConfig?.temperature}`);
        console.log(`   - Max tokens: ${capturedApiCall.data.generationConfig?.maxOutputTokens}\n`);
      }
    } catch (error) {
      console.log('❌ Email generation failed:', error.message);
    }

    // Restore original method
    aiService.client.post = originalPost;

    // Test 3: Configuration validation
    console.log('🔍 Test 3: Configuration Validation');
    const expectedUrl = 'https://generativelanguage.googleapis.com/v1beta/models';
    if (aiService.geminiBaseUrl === expectedUrl) {
      console.log('✅ Correct Gemini API base URL configured');
    } else {
      console.log('❌ Incorrect API base URL:', aiService.geminiBaseUrl);
    }

    if (aiService.model.includes('gemini')) {
      console.log('✅ Using Gemini model:', aiService.model);
    } else {
      console.log('❌ Not using Gemini model:', aiService.model);
    }

    console.log('\n🎉 Google Gemini API Integration Test Completed!');
    console.log('✅ Migration from OpenAI to Google Gemini appears successful');

  } catch (error) {
    console.error('❌ Integration test failed:', error.message);
    console.error(error.stack);
  }
}

// Run the test
testGeminiIntegration().catch(console.error);
