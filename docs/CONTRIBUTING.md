# Contributing to AI SDR Agent

Thank you for your interest in contributing to the AI SDR Agent project! This document provides guidelines and information for contributors.

## 🤝 How to Contribute

### Reporting Issues

1. **Search existing issues** first to avoid duplicates
2. **Use the issue template** when creating new issues
3. **Provide detailed information** including:
   - Steps to reproduce
   - Expected vs actual behavior
   - Environment details
   - Screenshots if applicable

### Submitting Pull Requests

1. **Fork the repository** and create a new branch
2. **Follow the naming convention**: `feature/description` or `fix/description`
3. **Make your changes** following our coding standards
4. **Write or update tests** for your changes
5. **Update documentation** if necessary
6. **Submit a pull request** with a clear description

## 🏗️ Development Setup

### Prerequisites

- Node.js 18.x or later
- Python 3.10+ (for backend services)
- AWS CLI configured
- Git

### Local Development

1. **Clone your fork**
   ```bash
   git clone https://github.com/your-username/AI_SDR.git
   cd AI_SDR
   ```

2. **Install dependencies**
   ```bash
   # Frontend
   cd src/frontend
   npm install

   # Backend
   cd ../backend
   npm install
   ```

3. **Set up environment**
   ```bash
   cp config/.env.example .env
   # Edit .env with your configuration
   ```

4. **Start development servers**
   ```bash
   # Frontend (in one terminal)
   cd src/frontend
   npm start

   # Backend (in another terminal)
   cd src/backend
   serverless offline
   ```

## 📝 Coding Standards

### General Guidelines

- **Write clean, readable code** with meaningful variable names
- **Follow existing code style** and patterns
- **Add comments** for complex logic
- **Keep functions small** and focused on single responsibilities
- **Use TypeScript** where applicable for type safety

### Frontend (React.js)

- Use **functional components** with hooks
- Follow **React best practices** for state management
- Use **Tailwind CSS** for styling
- Implement **responsive design** principles
- Write **accessible components** (ARIA labels, semantic HTML)

### Backend (Node.js/Python)

- Follow **RESTful API** design principles
- Implement **proper error handling**
- Use **async/await** for asynchronous operations
- Validate **input data** thoroughly
- Follow **security best practices**

### Testing

- Write **unit tests** for all new features
- Maintain **test coverage** above 80%
- Include **integration tests** for API endpoints
- Add **end-to-end tests** for critical user flows

## 🔧 Project Structure

### Directory Organization

```
AI_SDR/
├── docs/                    # Documentation
├── src/                     # Source code
│   ├── backend/            # Backend services
│   ├── frontend/           # Frontend application
│   └── shared/             # Shared utilities
├── infrastructure/         # Infrastructure as Code
├── tests/                  # Test files
├── scripts/                # Utility scripts
└── config/                 # Configuration files
```

### File Naming Conventions

- **Components**: PascalCase (e.g., `LeadList.jsx`)
- **Utilities**: camelCase (e.g., `apiHelpers.js`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `API_ENDPOINTS.js`)
- **Documentation**: kebab-case (e.g., `contributing.md`)

## 🧪 Testing Guidelines

### Running Tests

```bash
# All tests
npm test

# Frontend tests
cd src/frontend && npm test

# Backend tests
cd src/backend && npm test

# E2E tests
npm run test:e2e
```

### Writing Tests

- **Unit tests**: Test individual functions and components
- **Integration tests**: Test API endpoints and service interactions
- **E2E tests**: Test complete user workflows
- **Mock external services** to avoid dependencies and costs

## 📚 Documentation

### Documentation Standards

- **Keep documentation up-to-date** with code changes
- **Use clear, concise language**
- **Include code examples** where helpful
- **Follow markdown best practices**
- **Update README.md** for significant changes

### Documentation Types

- **API Documentation**: Document all endpoints with examples
- **Component Documentation**: Document React components with props
- **Architecture Documentation**: Keep system design docs current
- **User Documentation**: Maintain user guides and tutorials

## 🚀 Deployment

### Development Deployment

- **Use feature branches** for development
- **Test thoroughly** before merging
- **Deploy to staging** environment first
- **Get code review** before production deployment

### Production Deployment

- **Only deploy from main branch**
- **Use automated CI/CD** pipelines
- **Monitor deployment** for issues
- **Have rollback plan** ready

## 🔒 Security Guidelines

### Security Best Practices

- **Never commit secrets** or API keys
- **Use environment variables** for configuration
- **Validate all inputs** to prevent injection attacks
- **Follow OWASP guidelines** for web security
- **Keep dependencies updated** to patch vulnerabilities

### Reporting Security Issues

- **Do not create public issues** for security vulnerabilities
- **Email security concerns** to <EMAIL>
- **Provide detailed information** about the vulnerability
- **Allow time for fix** before public disclosure

## 📋 Code Review Process

### For Contributors

1. **Create descriptive pull requests** with clear titles
2. **Reference related issues** in PR description
3. **Respond to feedback** promptly and professionally
4. **Update your branch** if requested
5. **Squash commits** before merging if requested

### For Reviewers

1. **Review code thoroughly** for functionality and style
2. **Test the changes** locally if possible
3. **Provide constructive feedback**
4. **Approve when ready** or request changes
5. **Be respectful and helpful** in comments

## 🎯 Project Goals

### Primary Objectives

- **Automate sales processes** for mining equipment suppliers
- **Improve lead quality** through AI-powered scoring
- **Increase conversion rates** with personalized outreach
- **Provide actionable insights** through analytics
- **Maintain cost efficiency** through smart technology choices

### Success Metrics

- **Lead conversion rate** improvement
- **Time savings** for sales teams
- **User satisfaction** scores
- **System reliability** and uptime
- **Cost per lead** reduction

## 📞 Getting Help

### Resources

- **Documentation**: Check `/docs` directory first
- **Issues**: Search existing GitHub issues
- **Discussions**: Use GitHub Discussions for questions
- **Email**: Contact <EMAIL> for urgent matters

### Communication

- **Be respectful** and professional
- **Provide context** when asking questions
- **Search existing resources** before asking
- **Help others** when you can

## 📄 License

By contributing to this project, you agree that your contributions will be licensed under the MIT License.

---

Thank you for contributing to the AI SDR Agent project! Your efforts help make sales automation more accessible and effective for mining equipment suppliers.
