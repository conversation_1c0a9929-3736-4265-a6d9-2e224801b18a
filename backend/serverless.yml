service: ai-sdr-backend

frameworkVersion: '3'

provider:
  name: aws
  runtime: nodejs18.x
  region: ${opt:region, 'us-east-1'}
  stage: ${opt:stage, 'dev'}
  environment:
    STAGE: ${self:provider.stage}
    REGION: ${self:provider.region}
    DYNAMODB_TABLE_PREFIX: ${self:service}-${self:provider.stage}
    LEADS_TABLE: ${self:provider.environment.DYNAMODB_TABLE_PREFIX}-leads
    CAMPAIGNS_TABLE: ${self:provider.environment.DYNAMODB_TABLE_PREFIX}-campaigns
    USERS_TABLE: ${self:provider.environment.DYNAMODB_TABLE_PREFIX}-users
    JOBS_TABLE: ${self:provider.environment.DYNAMODB_TABLE_PREFIX}-jobs
    OUTREACH_QUEUE: ${self:provider.environment.DYNAMODB_TABLE_PREFIX}-outreach
    LEAD_ENRICHMENT_QUEUE: ${self:provider.environment.DYNAMODB_TABLE_PREFIX}-lead-enrichment
  iamRoleStatements:
    - Effect: Allow
      Action:
        - dynamodb:Query
        - dynamodb:Scan
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
      Resource:
        - "arn:aws:dynamodb:${self:provider.region}:*:table/${self:provider.environment.DYNAMODB_TABLE_PREFIX}-*"
    - Effect: Allow
      Action:
        - sqs:SendMessage
        - sqs:ReceiveMessage
        - sqs:DeleteMessage
      Resource:
        - "arn:aws:sqs:${self:provider.region}:*:${self:provider.environment.DYNAMODB_TABLE_PREFIX}-*"
    - Effect: Allow
      Action:
        - cloudwatch:GetMetricData
        - cloudwatch:PutMetricData
      Resource: "*"

functions:
  # Authentication Service
  auth:
    handler: services/auth/handler.main
    events:
      - http:
          path: auth/{proxy+}
          method: ANY
          cors: true

  # Leads Service
  leads:
    handler: services/leads/handler.main
    events:
      - http:
          path: leads/{proxy+}
          method: ANY
          cors: true

  # Outreach Service
  outreach:
    handler: services/outreach/handler.main
    events:
      - http:
          path: outreach/{proxy+}
          method: ANY
          cors: true

  # Analytics Service
  analytics:
    handler: services/analytics/handler.main
    events:
      - http:
          path: analytics/{proxy+}
          method: ANY
          cors: true

  # Integrations Service
  integrations:
    handler: services/integrations/handler.main
    events:
      - http:
          path: integrations/{proxy+}
          method: ANY
          cors: true

resources:
  Resources:
    # DynamoDB Tables
    LeadsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:provider.environment.DYNAMODB_TABLE_PREFIX}-leads
        AttributeDefinitions:
          - AttributeName: leadId
            AttributeType: S
        KeySchema:
          - AttributeName: leadId
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST

    CampaignsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:provider.environment.DYNAMODB_TABLE_PREFIX}-campaigns
        AttributeDefinitions:
          - AttributeName: campaignId
            AttributeType: S
        KeySchema:
          - AttributeName: campaignId
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST

    UsersTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:provider.environment.DYNAMODB_TABLE_PREFIX}-users
        AttributeDefinitions:
          - AttributeName: userId
            AttributeType: S
        KeySchema:
          - AttributeName: userId
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST

    JobsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:provider.environment.DYNAMODB_TABLE_PREFIX}-jobs
        AttributeDefinitions:
          - AttributeName: jobId
            AttributeType: S
        KeySchema:
          - AttributeName: jobId
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST

    # SQS Queues
    OutreachQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.environment.OUTREACH_QUEUE}
        VisibilityTimeout: 300

    LeadEnrichmentQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.environment.LEAD_ENRICHMENT_QUEUE}
        VisibilityTimeout: 300

# plugins:
#   - serverless-offline
