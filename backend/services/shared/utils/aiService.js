const axios = require('axios');
const { logger } = require('./api');

/**
 * AI Service for Google Gemini API integration
 * Provides intelligent lead scoring, content generation, and personalization
 */
class AIService {
  constructor() {
    this.geminiApiKey = process.env.GEMINI_API_KEY;
    this.geminiBaseUrl = 'https://generativelanguage.googleapis.com/v1beta/models';
    this.model = process.env.GEMINI_MODEL || 'gemini-pro';

    if (!this.geminiApiKey) {
      logger.warn('Google Gemini API key not configured. AI features will be disabled.');
    }

    this.client = axios.create({
      baseURL: this.geminiBaseUrl,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  /**
   * Check if AI service is available
   */
  isAvailable() {
    return !!this.geminiApiKey;
  }

  /**
   * Generate personalized email content for a lead
   * @param {Object} lead - Lead information
   * @param {Object} campaign - Campaign details
   * @returns {Promise<Object>} Generated email content
   */
  async generatePersonalizedEmail(lead, campaign) {
    if (!this.isAvailable()) {
      throw new Error('AI service not available - Google Gemini API key not configured');
    }

    try {
      const prompt = this.buildEmailPrompt(lead, campaign);
      const systemPrompt = 'You are an expert sales development representative (SDR) specializing in B2B outreach for the mining industry. Generate professional, personalized emails that are concise, value-focused, and likely to get responses.';
      const fullPrompt = `${systemPrompt}\n\n${prompt}`;

      const response = await this.client.post(`/${this.model}:generateContent?key=${this.geminiApiKey}`, {
        contents: [
          {
            parts: [
              {
                text: fullPrompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.7,
          topP: 1,
          maxOutputTokens: 500
        }
      });

      const generatedContent = response.data.candidates[0].content.parts[0].text;
      
      return this.parseEmailContent(generatedContent);
    } catch (error) {
      logger.error('Failed to generate personalized email:', error.response?.data || error.message);
      throw new Error(`AI email generation failed: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  /**
   * Calculate AI-powered lead score
   * @param {Object} lead - Lead information
   * @returns {Promise<Object>} Lead score and reasoning
   */
  async calculateLeadScore(lead) {
    if (!this.isAvailable()) {
      // Fallback to basic scoring
      return this.calculateBasicLeadScore(lead);
    }

    try {
      const prompt = this.buildScoringPrompt(lead);
      const systemPrompt = 'You are an expert lead qualification specialist for B2B mining industry sales. Analyze leads and provide scores from 0-100 with detailed reasoning. Focus on company size, revenue, industry fit, contact quality, and buying signals.';
      const fullPrompt = `${systemPrompt}\n\n${prompt}`;

      const response = await this.client.post(`/${this.model}:generateContent?key=${this.geminiApiKey}`, {
        contents: [
          {
            parts: [
              {
                text: fullPrompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.3,
          topP: 1,
          maxOutputTokens: 300
        }
      });

      const analysis = response.data.candidates[0].content.parts[0].text;
      return this.parseLeadScore(analysis);
    } catch (error) {
      logger.error('Failed to calculate AI lead score:', error.response?.data || error.message);
      // Fallback to basic scoring
      return this.calculateBasicLeadScore(lead);
    }
  }

  /**
   * Generate campaign strategy recommendations
   * @param {Array} leads - Array of leads
   * @param {Object} campaignGoals - Campaign objectives
   * @returns {Promise<Object>} Campaign strategy recommendations
   */
  async generateCampaignStrategy(leads, campaignGoals) {
    if (!this.isAvailable()) {
      return this.generateBasicCampaignStrategy(leads, campaignGoals);
    }

    try {
      const prompt = this.buildCampaignPrompt(leads, campaignGoals);
      const systemPrompt = 'You are a strategic sales campaign expert specializing in B2B mining industry outreach. Analyze lead data and provide actionable campaign recommendations including messaging, timing, channels, and personalization strategies.';
      const fullPrompt = `${systemPrompt}\n\n${prompt}`;

      const response = await this.client.post(`/${this.model}:generateContent?key=${this.geminiApiKey}`, {
        contents: [
          {
            parts: [
              {
                text: fullPrompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.6,
          topP: 1,
          maxOutputTokens: 800
        }
      });

      const strategy = response.data.candidates[0].content.parts[0].text;
      return this.parseCampaignStrategy(strategy);
    } catch (error) {
      logger.error('Failed to generate campaign strategy:', error.response?.data || error.message);
      return this.generateBasicCampaignStrategy(leads, campaignGoals);
    }
  }

  /**
   * Build email generation prompt
   */
  buildEmailPrompt(lead, campaign) {
    return `Generate a personalized B2B sales email for the following lead:

Company: ${lead.companyName || 'Unknown'}
Contact: ${lead.contactName || 'Unknown'}
Industry: ${lead.industry || 'Mining'}
Location: ${lead.location || 'Unknown'}
Revenue: ${lead.revenue || 'Unknown'}
Lead Score: ${lead.leadScore || 'Unknown'}

Campaign Type: ${campaign.type || 'Cold Outreach'}
Campaign Goal: ${campaign.goal || 'Generate interest and book meetings'}
Value Proposition: ${campaign.valueProposition || 'Improve operational efficiency and reduce costs'}

Requirements:
- Subject line (compelling, under 50 characters)
- Email body (professional, concise, under 150 words)
- Clear call-to-action
- Personalized to the company and industry
- Focus on value and benefits
- Professional tone

Format the response as:
SUBJECT: [subject line]
BODY: [email body]
CTA: [call to action]`;
  }

  /**
   * Build lead scoring prompt
   */
  buildScoringPrompt(lead) {
    return `Analyze this B2B mining industry lead and provide a qualification score:

Company: ${lead.companyName || 'Unknown'}
Industry: ${lead.industry || 'Unknown'}
Revenue: ${lead.revenue || 'Unknown'}
Location: ${lead.location || 'Unknown'}
Employee Count: ${lead.employees || 'Unknown'}
Contact Email: ${lead.contactEmail ? 'Available' : 'Not available'}
Phone: ${lead.phone ? 'Available' : 'Not available'}
Source: ${lead.source || 'Unknown'}
Tags: ${lead.tags ? lead.tags.join(', ') : 'None'}

Provide:
1. Overall score (0-100)
2. Key strengths
3. Potential concerns
4. Recommended next steps

Format: SCORE: [number] | STRENGTHS: [list] | CONCERNS: [list] | NEXT_STEPS: [recommendations]`;
  }

  /**
   * Build campaign strategy prompt
   */
  buildCampaignPrompt(leads, goals) {
    const leadSummary = leads.slice(0, 10).map(lead => 
      `${lead.companyName} (${lead.industry}, ${lead.revenue || 'Unknown revenue'})`
    ).join(', ');

    return `Analyze these mining industry leads and create a campaign strategy:

Leads Sample: ${leadSummary}
Total Leads: ${leads.length}
Campaign Goals: ${JSON.stringify(goals)}

Provide strategic recommendations for:
1. Messaging themes
2. Outreach sequence timing
3. Channel mix (email, phone, LinkedIn)
4. Personalization approach
5. Success metrics

Format as structured recommendations.`;
  }

  /**
   * Parse email content from AI response
   */
  parseEmailContent(content) {
    const lines = content.split('\n');
    let subject = '';
    let body = '';
    let cta = '';

    for (const line of lines) {
      if (line.startsWith('SUBJECT:')) {
        subject = line.replace('SUBJECT:', '').trim();
      } else if (line.startsWith('BODY:')) {
        body = line.replace('BODY:', '').trim();
      } else if (line.startsWith('CTA:')) {
        cta = line.replace('CTA:', '').trim();
      } else if (body && !line.startsWith('CTA:') && line.trim()) {
        body += ' ' + line.trim();
      }
    }

    return {
      subject: subject || 'Partnership Opportunity',
      body: body || content.substring(0, 300),
      callToAction: cta || 'Would you be available for a brief call this week?',
      generatedAt: new Date().toISOString(),
      confidence: 0.8
    };
  }

  /**
   * Parse lead score from AI response
   */
  parseLeadScore(analysis) {
    const scoreMatch = analysis.match(/SCORE:\s*(\d+)/i);
    const score = scoreMatch ? parseInt(scoreMatch[1]) : 50;

    const strengthsMatch = analysis.match(/STRENGTHS:\s*([^|]+)/i);
    const strengths = strengthsMatch ? strengthsMatch[1].trim().split(',').map(s => s.trim()) : [];

    const concernsMatch = analysis.match(/CONCERNS:\s*([^|]+)/i);
    const concerns = concernsMatch ? concernsMatch[1].trim().split(',').map(s => s.trim()) : [];

    const stepsMatch = analysis.match(/NEXT_STEPS:\s*(.+)/i);
    const nextSteps = stepsMatch ? stepsMatch[1].trim() : 'Follow up with personalized outreach';

    return {
      score,
      strengths,
      concerns,
      nextSteps,
      analysis: analysis,
      calculatedAt: new Date().toISOString(),
      method: 'ai-powered'
    };
  }

  /**
   * Parse campaign strategy from AI response
   */
  parseCampaignStrategy(strategy) {
    return {
      strategy: strategy,
      recommendations: this.extractRecommendations(strategy),
      generatedAt: new Date().toISOString(),
      confidence: 0.75
    };
  }

  /**
   * Extract structured recommendations from strategy text
   */
  extractRecommendations(strategy) {
    // Simple extraction - could be enhanced with more sophisticated parsing
    return {
      messaging: strategy.includes('messaging') ? 'Value-focused messaging recommended' : 'Standard messaging',
      timing: strategy.includes('timing') ? 'Multi-touch sequence recommended' : 'Single touch',
      channels: ['email', 'phone'],
      personalization: 'Company and industry-specific',
      metrics: ['open rate', 'response rate', 'meeting bookings']
    };
  }

  /**
   * Fallback basic lead scoring when AI is not available
   */
  calculateBasicLeadScore(lead) {
    let score = 0;
    
    // Company size scoring
    if (lead.revenue) {
      const revenue = parseFloat(lead.revenue.toString().replace(/[^\d.]/g, ''));
      if (revenue >= 50) score += 30;
      else if (revenue >= 20) score += 25;
      else if (revenue >= 10) score += 20;
      else score += 10;
    }

    // Contact information scoring
    if (lead.contactEmail) score += 20;
    if (lead.phone) score += 15;
    if (lead.contactName && lead.contactName !== 'Unknown') score += 10;

    // Industry fit scoring
    if (lead.industry && lead.industry.toLowerCase().includes('mining')) score += 15;

    // Source quality scoring
    if (lead.source && lead.source.includes('enrichment')) score += 10;

    return {
      score: Math.min(score, 100),
      strengths: ['Basic qualification completed'],
      concerns: ['Limited data available'],
      nextSteps: 'Gather more information and initiate contact',
      analysis: 'Basic scoring algorithm applied',
      calculatedAt: new Date().toISOString(),
      method: 'basic-algorithm'
    };
  }

  /**
   * Fallback basic campaign strategy when AI is not available
   */
  generateBasicCampaignStrategy(leads, goals) {
    return {
      strategy: 'Multi-touch email and phone campaign with industry-specific messaging',
      recommendations: {
        messaging: 'Focus on operational efficiency and cost reduction',
        timing: '3-touch sequence over 2 weeks',
        channels: ['email', 'phone'],
        personalization: 'Company name and industry',
        metrics: ['open rate', 'response rate', 'meetings booked']
      },
      generatedAt: new Date().toISOString(),
      confidence: 0.6
    };
  }
}

module.exports = { AIService };
