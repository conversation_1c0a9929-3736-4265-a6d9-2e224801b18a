# Environment Configuration Example
# Copy this file to .env and fill in your actual values

# Application Environment
NODE_ENV=development
PORT=3000

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key

# Database Configuration
DYNAMODB_TABLE_PREFIX=ai-sdr-dev
DYNAMODB_LEADS_TABLE=ai-sdr-dev-leads
DYNAMODB_USERS_TABLE=ai-sdr-dev-users
DYNAMODB_JOBS_TABLE=ai-sdr-dev-jobs

# Authentication
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=24h

# AI Services
RELEVANCE_AI_API_KEY=your_relevance_ai_api_key
RELEVANCE_AI_PROJECT_ID=your_project_id
GOOGLE_GEMINI_API_KEY=your_gemini_api_key

# Communication Services
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=AI SDR Agent

TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

LINKEDIN_CLIENT_ID=your_linkedin_client_id
LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret

# Scheduling Services
CALENDLY_API_KEY=your_calendly_api_key
GOOGLE_CALENDAR_CLIENT_ID=your_google_calendar_client_id
GOOGLE_CALENDAR_CLIENT_SECRET=your_google_calendar_client_secret

# Data Enrichment Services
APOLLO_API_KEY=your_apollo_api_key
CLEARBIT_API_KEY=your_clearbit_api_key

# CRM Integrations
HUBSPOT_API_KEY=your_hubspot_api_key
ZOHO_CLIENT_ID=your_zoho_client_id
ZOHO_CLIENT_SECRET=your_zoho_client_secret
SALESFORCE_CLIENT_ID=your_salesforce_client_id
SALESFORCE_CLIENT_SECRET=your_salesforce_client_secret

# Monitoring and Logging
LOG_LEVEL=info
CLOUDWATCH_LOG_GROUP=/aws/lambda/ai-sdr-agent

# Frontend Configuration
REACT_APP_API_BASE_URL=https://api.yourdomain.com
REACT_APP_ENVIRONMENT=development

# Security
CORS_ORIGIN=http://localhost:3000
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Feature Flags
ENABLE_SMS_OUTREACH=false
ENABLE_LINKEDIN_OUTREACH=false
ENABLE_ADVANCED_ANALYTICS=false
