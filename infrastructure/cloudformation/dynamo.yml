AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for DynamoDB tables

Resources:
  UsersTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: Users
      AttributeDefinitions:
        - AttributeName: user_id
          AttributeType: S
      KeySchema:
        - AttributeName: user_id
          KeyType: HASH
      ProvisionedThroughput:
        ReadCapacityUnits: 5
        WriteCapacityUnits: 5

  LeadsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: Leads
      AttributeDefinitions:
        - AttributeName: lead_id
          AttributeType: S
      KeySchema:
        - AttributeName: lead_id
          KeyType: HASH
      ProvisionedThroughput:
        ReadCapacityUnits: 5
        WriteCapacityUnits: 5

  CampaignsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: Campaigns
      AttributeDefinitions:
        - AttributeName: campaign_id
          AttributeType: S
      KeySchema:
        - AttributeName: campaign_id
          KeyType: HASH
      ProvisionedThroughput:
        ReadCapacityUnits: 5
        WriteCapacityUnits: 5

  JobsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: Jobs
      AttributeDefinitions:
        - AttributeName: job_id
          AttributeType: S
      KeySchema:
        - AttributeName: job_id
          KeyType: HASH
      ProvisionedThroughput:
        ReadCapacityUnits: 5
        WriteCapacityUnits: 5