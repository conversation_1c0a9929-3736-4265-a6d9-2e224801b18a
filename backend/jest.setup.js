// Global test setup

// Setup test environment variables
process.env.NODE_ENV = 'test';
process.env.AWS_REGION = 'us-east-1';
process.env.STAGE = 'test';
process.env.DYNAMODB_TABLE_PREFIX = 'ai-sdr-test';
process.env.USERS_TABLE = 'ai-sdr-test-users';
process.env.LEADS_TABLE = 'ai-sdr-test-leads';
process.env.CAMPAIGNS_TABLE = 'ai-sdr-test-campaigns';
process.env.JOBS_TABLE = 'ai-sdr-test-jobs';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.LOG_LEVEL = 'error';

// Setup before each test
beforeEach(() => {
  // Clear all mocks before each test
  jest.clearAllMocks();
});

// Suppress console logs during tests unless explicitly needed
const originalConsole = console;
global.console = {
  ...originalConsole,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};

// Restore console for specific tests if needed
global.restoreConsole = () => {
  global.console = originalConsole;
};

// Mock timers
jest.useFakeTimers();

// Increase timeout for async operations
jest.setTimeout(30000);
