const { connectCRM, syncLeads } = require('../../shared/utils/crm');

module.exports.crmHandler = async (event) => {
  try {
    const { crmType, credentials } = JSON.parse(event.body);
    const connection = await connectCRM(crmType, credentials);
    
    return {
      statusCode: 200,
      body: JSON.stringify({ success: true, connection })
    };
  } catch (error) {
    return {
      statusCode: 500,
      body: JSON.stringify({ error: error.message })
    };
  }
};

module.exports.syncHandler = async (event) => {
  try {
    const { crmType, direction } = JSON.parse(event.body);
    const result = await syncLeads(crmType, direction);
    
    return {
      statusCode: 200,
      body: JSON.stringify(result)
    };
  } catch (error) {
    return {
      statusCode: 500,
      body: JSON.stringify({ error: error.message })
    };
  }
};