{"name": "ai-sdr-backend", "version": "1.0.0", "description": "Backend services for AI SDR Agent", "main": "index.js", "scripts": {"start": "node server.js", "deploy": "serverless deploy", "deploy:dev": "serverless deploy --stage dev", "deploy:staging": "serverless deploy --stage staging", "deploy:prod": "serverless deploy --stage prod", "test": "jest", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "dev": "node local-server.js", "offline": "serverless offline", "lint": "eslint . --ext .js,.ts", "lint:fix": "eslint . --ext .js,.ts --fix"}, "dependencies": {"aws-lambda": "^1.0.7", "aws-sdk": "^2.1400.0", "axios": "^1.4.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "express": "^4.18.2", "joi": "^17.9.0", "jsonwebtoken": "^9.0.0", "uuid": "^9.0.0"}, "devDependencies": {"@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "@typescript-eslint/eslint-plugin": "^5.59.0", "@typescript-eslint/parser": "^5.59.0", "babel-jest": "^29.5.0", "eslint": "^8.40.0", "jest": "^29.5.0", "serverless": "^3.40.0", "serverless-offline": "^12.0.0", "serverless-webpack": "^5.11.0", "supertest": "^6.3.0", "typescript": "^5.0.0", "webpack": "^5.80.0"}, "author": "GEMDevEng", "license": "MIT", "keywords": []}