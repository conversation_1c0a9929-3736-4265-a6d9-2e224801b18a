# Staging Environment Configuration
# AI SDR Backend - Staging Stage

# Service Configuration
service:
  name: ai-sdr-backend-staging
  stage: staging
  region: us-east-1

# Lambda Configuration
lambda:
  timeout: 60
  memorySize: 1024
  runtime: nodejs18.x
  environment:
    NODE_ENV: staging
    LOG_LEVEL: info
    CORS_ORIGIN: "https://staging.ai-sdr.com"

# DynamoDB Configuration
dynamodb:
  tables:
    leads:
      tableName: ai-sdr-leads-staging
      billingMode: PAY_PER_REQUEST
      pointInTimeRecovery: true
      streamSpecification:
        streamViewType: NEW_AND_OLD_IMAGES
    campaigns:
      tableName: ai-sdr-campaigns-staging
      billingMode: PAY_PER_REQUEST
      pointInTimeRecovery: true
    users:
      tableName: ai-sdr-users-staging
      billingMode: PAY_PER_REQUEST
      pointInTimeRecovery: true

# API Gateway Configuration
apiGateway:
  restApiId: ${env:STAGING_API_GATEWAY_REST_API_ID, ''}
  restApiRootResourceId: ${env:STAGING_API_GATEWAY_ROOT_RESOURCE_ID, ''}
  minimumCompressionSize: 1024
  binaryMediaTypes:
    - '*/*'
  requestValidators:
    validateRequestBody: true
    validateRequestParameters: true

# External Services Configuration
external:
  sendgrid:
    apiKey: ${env:STAGING_SENDGRID_API_KEY}
    fromEmail: ${env:STAGING_SENDGRID_FROM_EMAIL, '<EMAIL>'}
    fromName: ${env:STAGING_SENDGRID_FROM_NAME, 'AI SDR Staging'}
  
  twilio:
    accountSid: ${env:STAGING_TWILIO_ACCOUNT_SID}
    authToken: ${env:STAGING_TWILIO_AUTH_TOKEN}
    fromNumber: ${env:STAGING_TWILIO_FROM_NUMBER}
  
  hubspot:
    apiKey: ${env:STAGING_HUBSPOT_API_KEY}
    portalId: ${env:STAGING_HUBSPOT_PORTAL_ID}
  
  gemini:
    apiKey: ${env:STAGING_GEMINI_API_KEY}
    model: ${env:STAGING_GEMINI_MODEL, 'gemini-pro'}
    maxTokens: ${env:STAGING_GEMINI_MAX_TOKENS, '800'}
  
  hunter:
    apiKey: ${env:STAGING_HUNTER_API_KEY}

# Security Configuration
security:
  jwt:
    secret: ${env:STAGING_JWT_SECRET}
    expiresIn: ${env:STAGING_JWT_EXPIRES_IN, '12h'}
  
  cors:
    origin: 
      - https://staging.ai-sdr.com
      - https://staging-admin.ai-sdr.com
    credentials: true
    methods:
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
    headers:
      - Content-Type
      - Authorization
      - X-Requested-With

# Monitoring Configuration
monitoring:
  cloudwatch:
    enabled: true
    logRetentionInDays: 30
  
  xray:
    enabled: true
  
  alarms:
    enabled: true
    errorRate:
      threshold: 5
      period: 300
    latency:
      threshold: 5000
      period: 300

# VPC Configuration (if needed)
vpc:
  enabled: false
  securityGroupIds: []
  subnetIds: []

# Custom Configuration
custom:
  webpack:
    webpackConfig: 'webpack.config.js'
    includeModules: true
    packager: 'npm'
  
  alerts:
    stages:
      - staging
    topics:
      alarm: ${env:STAGING_SNS_ALARM_TOPIC}
    alarms:
      - functionErrors
      - functionDuration
      - functionThrottles

# Plugins
plugins:
  - serverless-webpack
  - serverless-dotenv-plugin
  - serverless-plugin-aws-alerts

# Package Configuration
package:
  individually: true
  exclude:
    - .git/**
    - .gitignore
    - README.md
    - .env*
    - tests/**
    - coverage/**
    - docs/**
    - '*.md'
    - node_modules/aws-sdk/**
