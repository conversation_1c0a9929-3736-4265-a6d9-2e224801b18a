const axios = require('axios');
const { logger } = require('./api');

/**
 * Lead Enrichment Service Integration
 * Connects Node.js backend with Python lead enrichment microservice
 */
class LeadEnrichmentClient {
  constructor() {
    this.baseURL = process.env.LEAD_ENRICHMENT_SERVICE_URL || 'http://localhost:5000';
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add request/response interceptors for logging
    this.client.interceptors.request.use(
      (config) => {
        logger.info(`Lead Enrichment Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        logger.error('Lead Enrichment Request Error:', error);
        return Promise.reject(error);
      }
    );

    this.client.interceptors.response.use(
      (response) => {
        logger.info(`Lead Enrichment Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        logger.error('Lead Enrichment Response Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  /**
   * Check if the lead enrichment service is healthy
   */
  async healthCheck() {
    try {
      const response = await this.client.get('/health');
      return response.data;
    } catch (error) {
      logger.error('Lead enrichment service health check failed:', error.message);
      throw new Error('Lead enrichment service is unavailable');
    }
  }

  /**
   * Scrape leads from a given URL
   * @param {string} url - URL to scrape
   * @param {Object} selectors - CSS selectors for data extraction
   * @returns {Promise<Object>} Scraped leads data
   */
  async scrapeLeads(url, selectors = {}) {
    try {
      const response = await this.client.post('/scrape', {
        url,
        selectors
      });
      
      logger.info(`Successfully scraped ${response.data.leads_found} leads from ${url}`);
      return response.data;
    } catch (error) {
      logger.error(`Failed to scrape leads from ${url}:`, error.response?.data || error.message);
      throw new Error(`Lead scraping failed: ${error.response?.data?.error || error.message}`);
    }
  }

  /**
   * Enrich leads with additional data and filtering
   * @param {Array} leads - Array of lead objects
   * @param {Object} criteria - Filtering criteria
   * @returns {Promise<Object>} Enriched leads data
   */
  async enrichLeads(leads, criteria = {}) {
    try {
      const response = await this.client.post('/enrich', {
        leads,
        criteria
      });
      
      logger.info(`Successfully enriched ${response.data.leads_enriched} leads`);
      return response.data;
    } catch (error) {
      logger.error('Failed to enrich leads:', error.response?.data || error.message);
      throw new Error(`Lead enrichment failed: ${error.response?.data?.error || error.message}`);
    }
  }

  /**
   * Get enriched leads from the database
   * @param {number} limit - Maximum number of leads to return
   * @param {number} minScore - Minimum lead score threshold
   * @returns {Promise<Object>} Enriched leads from database
   */
  async getEnrichedLeads(limit = 100, minScore = 5) {
    try {
      const response = await this.client.get('/leads', {
        params: { limit, min_score: minScore }
      });
      
      logger.info(`Retrieved ${response.data.count} enriched leads`);
      return response.data;
    } catch (error) {
      logger.error('Failed to get enriched leads:', error.response?.data || error.message);
      throw new Error(`Failed to retrieve enriched leads: ${error.response?.data?.error || error.message}`);
    }
  }

  /**
   * Add a new scraping source
   * @param {Object} source - Source configuration
   * @returns {Promise<Object>} Success response
   */
  async addScrapingSource(source) {
    try {
      const response = await this.client.post('/sources', source);
      logger.info(`Added new scraping source: ${source.name}`);
      return response.data;
    } catch (error) {
      logger.error('Failed to add scraping source:', error.response?.data || error.message);
      throw new Error(`Failed to add scraping source: ${error.response?.data?.error || error.message}`);
    }
  }

  /**
   * Get all scraping sources
   * @returns {Promise<Array>} List of scraping sources
   */
  async getScrapingSources() {
    try {
      const response = await this.client.get('/sources');
      return response.data;
    } catch (error) {
      logger.error('Failed to get scraping sources:', error.response?.data || error.message);
      throw new Error(`Failed to retrieve scraping sources: ${error.response?.data?.error || error.message}`);
    }
  }

  /**
   * Comprehensive lead discovery and enrichment workflow
   * @param {Object} config - Configuration for lead discovery
   * @returns {Promise<Object>} Complete workflow results
   */
  async discoverAndEnrichLeads(config) {
    const {
      sources = [],
      criteria = {
        revenue_range: ['10M-50M', '20M-50M'],
        target_locations: ['Africa', 'Canada', 'DRC', 'South Africa'],
        min_confidence: 0.5
      },
      limit = 50
    } = config;

    try {
      const results = {
        total_scraped: 0,
        total_enriched: 0,
        high_quality_leads: [],
        sources_processed: 0,
        errors: []
      };

      // Process each source
      for (const source of sources) {
        try {
          logger.info(`Processing source: ${source.name}`);
          
          // Scrape leads from source
          const scrapeResult = await this.scrapeLeads(source.url, source.selectors);
          results.total_scraped += scrapeResult.leads_found;
          results.sources_processed++;

          // Enrich the scraped leads
          if (scrapeResult.leads && scrapeResult.leads.length > 0) {
            const enrichResult = await this.enrichLeads(scrapeResult.leads, criteria);
            results.total_enriched += enrichResult.leads_enriched;
            
            if (enrichResult.enriched_leads) {
              results.high_quality_leads.push(...enrichResult.enriched_leads);
            }
          }
        } catch (error) {
          logger.error(`Error processing source ${source.name}:`, error.message);
          results.errors.push({
            source: source.name,
            error: error.message
          });
        }
      }

      // Get final enriched leads from database
      const finalLeads = await this.getEnrichedLeads(limit, 8); // High score threshold
      results.final_leads = finalLeads.leads || [];

      logger.info(`Lead discovery completed: ${results.total_scraped} scraped, ${results.total_enriched} enriched`);
      return results;
    } catch (error) {
      logger.error('Lead discovery workflow failed:', error.message);
      throw new Error(`Lead discovery workflow failed: ${error.message}`);
    }
  }
}

// Default mining industry sources based on the guide
const DEFAULT_MINING_SOURCES = [
  {
    name: 'Mining Association of Canada',
    url: 'https://mining.ca/members/',
    selectors: {
      company_listing: '.member-listing',
      company_name: '.member-name',
      contact_email: '.member-email',
      website: '.member-website',
      location: '.member-location'
    }
  },
  {
    name: 'African Mining Network',
    url: 'https://www.miningafrica.net/companies',
    selectors: {
      company_listing: '.company-card',
      company_name: '.company-title',
      contact_email: '.contact-info .email',
      location: '.company-location',
      revenue: '.company-revenue'
    }
  }
];

// Export the client and default sources
module.exports = {
  LeadEnrichmentClient,
  DEFAULT_MINING_SOURCES
};
