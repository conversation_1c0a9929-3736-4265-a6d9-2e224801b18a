const Joi = require('joi');
const { DatabaseService } = require('../utils/database');
const { generateId, logger } = require('../utils/helpers');
const { ValidationError } = require('../utils/api');

// Campaign validation schema
const campaignSchema = Joi.object({
  campaignId: Joi.string().optional(),
  name: Joi.string().required().min(1).max(255),
  description: Joi.string().optional().max(1000),
  status: Joi.string().valid('draft', 'active', 'paused', 'completed', 'cancelled').default('draft'),
  channels: Joi.array().items(Joi.string().valid('email', 'sms', 'linkedin')).default([]),
  schedule: Joi.string().valid('immediate', 'scheduled', 'recurring').default('immediate'),
  scheduledAt: Joi.date().optional(),
  leads: Joi.array().items(Joi.string()).default([]),
  template: Joi.object({
    subject: Joi.string().optional(),
    body: Joi.string().optional(),
    variables: Joi.object().optional()
  }).optional(),
  settings: Joi.object({
    delayBetweenSends: Joi.number().min(0).optional(),
    maxSendsPerDay: Joi.number().min(1).optional(),
    trackOpens: Joi.boolean().default(true),
    trackClicks: Joi.boolean().default(true)
  }).optional(),
  tags: Joi.array().items(Joi.string()).default([]),
  createdBy: Joi.string().optional()
});

const updateCampaignSchema = campaignSchema.fork(['name'], (schema) => schema.optional());

class CampaignService extends DatabaseService {
  constructor() {
    super(process.env.CAMPAIGNS_TABLE || 'ai-sdr-dev-campaigns');
  }

  getPrimaryKey() {
    return 'campaignId';
  }

  /**
   * Create a new campaign with validation
   * @param {Object} campaignData - Campaign data
   * @returns {Promise<Object>} Created campaign
   */
  async createCampaign(campaignData) {
    // Validate input
    const validatedData = campaignSchema.validate(campaignData);
    if (validatedData.error) {
      throw new ValidationError('Invalid campaign data', validatedData.error.details);
    }

    const campaign = validatedData.value;

    // Generate ID if not provided
    if (!campaign.campaignId) {
      campaign.campaignId = generateId();
    }

    logger.info('Creating new campaign', { campaignId: campaign.campaignId, name: campaign.name });

    return await this.create(campaign);
  }

  /**
   * Get campaign by ID
   * @param {string} campaignId - Campaign ID
   * @returns {Promise<Object>} Campaign data
   */
  async getCampaign(campaignId) {
    return await this.get(campaignId);
  }

  /**
   * Update campaign with validation
   * @param {string} campaignId - Campaign ID
   * @param {Object} updates - Fields to update
   * @returns {Promise<Object>} Updated campaign
   */
  async updateCampaign(campaignId, updates) {
    // Validate updates
    const validatedUpdates = updateCampaignSchema.validate(updates);
    if (validatedUpdates.error) {
      throw new ValidationError('Invalid update data', validatedUpdates.error.details);
    }

    logger.info('Updating campaign', { campaignId, updates: Object.keys(validatedUpdates.value) });

    return await this.update(campaignId, validatedUpdates.value);
  }

  /**
   * Delete campaign
   * @param {string} campaignId - Campaign ID
   * @returns {Promise<void>}
   */
  async deleteCampaign(campaignId) {
    logger.info('Deleting campaign', { campaignId });
    return await this.delete(campaignId);
  }

  /**
   * Get all campaigns with optional filtering and pagination
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Campaigns with pagination metadata
   */
  async getCampaigns(options = {}) {
    const {
      filter = {},
      sort = 'createdAt',
      order = 'desc',
      page = 1,
      limit = 20
    } = options;

    logger.info('Fetching campaigns', { filter, sort, order, page, limit });

    const scanOptions = {};

    // Add filter expressions if provided
    if (Object.keys(filter).length > 0) {
      const filterExpressions = [];
      const expressionAttributeNames = {};
      const expressionAttributeValues = {};

      Object.entries(filter).forEach(([key, value], index) => {
        const attrName = `#attr${index}`;
        const attrValue = `:val${index}`;

        filterExpressions.push(`${attrName} = ${attrValue}`);
        expressionAttributeNames[attrName] = key;
        expressionAttributeValues[attrValue] = value;
      });

      if (filterExpressions.length > 0) {
        scanOptions.FilterExpression = filterExpressions.join(' AND ');
        scanOptions.ExpressionAttributeNames = expressionAttributeNames;
        scanOptions.ExpressionAttributeValues = expressionAttributeValues;
      }
    }

    const items = await this.scan(scanOptions);

    // Sort items
    items.sort((a, b) => {
      const aVal = a[sort];
      const bVal = b[sort];

      if (order === 'desc') {
        return bVal > aVal ? 1 : -1;
      } else {
        return aVal > bVal ? 1 : -1;
      }
    });

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedItems = items.slice(startIndex, endIndex);

    return {
      campaigns: paginatedItems,
      pagination: {
        total: items.length,
        page,
        limit,
        totalPages: Math.ceil(items.length / limit),
        hasNext: endIndex < items.length,
        hasPrev: page > 1
      }
    };
  }

  /**
   * Update campaign status
   * @param {string} campaignId - Campaign ID
   * @param {string} status - New status
   * @returns {Promise<Object>} Updated campaign
   */
  async updateStatus(campaignId, status) {
    const validStatuses = ['draft', 'active', 'paused', 'completed', 'cancelled'];
    if (!validStatuses.includes(status)) {
      throw new ValidationError(`Invalid status. Must be one of: ${validStatuses.join(', ')}`);
    }

    logger.info('Updating campaign status', { campaignId, status });

    return await this.update(campaignId, { status });
  }

  /**
   * Add leads to campaign
   * @param {string} campaignId - Campaign ID
   * @param {Array<string>} leadIds - Lead IDs to add
   * @returns {Promise<Object>} Updated campaign
   */
  async addLeads(campaignId, leadIds) {
    const campaign = await this.get(campaignId);
    const existingLeads = campaign.leads || [];
    const uniqueLeads = [...new Set([...existingLeads, ...leadIds])];

    logger.info('Adding leads to campaign', { campaignId, newLeads: leadIds.length, totalLeads: uniqueLeads.length });

    return await this.update(campaignId, { leads: uniqueLeads });
  }

  /**
   * Remove leads from campaign
   * @param {string} campaignId - Campaign ID
   * @param {Array<string>} leadIds - Lead IDs to remove
   * @returns {Promise<Object>} Updated campaign
   */
  async removeLeads(campaignId, leadIds) {
    const campaign = await this.get(campaignId);
    const existingLeads = campaign.leads || [];
    const filteredLeads = existingLeads.filter(leadId => !leadIds.includes(leadId));

    logger.info('Removing leads from campaign', { campaignId, removedLeads: leadIds.length, remainingLeads: filteredLeads.length });

    return await this.update(campaignId, { leads: filteredLeads });
  }

  /**
   * Get campaigns by status
   * @param {string} status - Campaign status
   * @returns {Promise<Array>} Campaigns with the specified status
   */
  async getCampaignsByStatus(status) {
    const scanOptions = {
      FilterExpression: '#status = :status',
      ExpressionAttributeNames: {
        '#status': 'status'
      },
      ExpressionAttributeValues: {
        ':status': status
      }
    };

    logger.info('Fetching campaigns by status', { status });

    return await this.scan(scanOptions);
  }

  /**
   * Get active campaigns
   * @returns {Promise<Array>} Active campaigns
   */
  async getActiveCampaigns() {
    return await this.getCampaignsByStatus('active');
  }
}

// Export singleton instance
const campaignService = new CampaignService();

module.exports = {
  CampaignService,
  campaignService,
  campaignSchema,
  updateCampaignSchema
};