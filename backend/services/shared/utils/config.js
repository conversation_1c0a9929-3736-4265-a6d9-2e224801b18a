const AWS = require('aws-sdk');
const { logger } = require('./api');

// Initialize AWS services
const secretsManager = new AWS.SecretsManager({
  region: process.env.AWS_REGION || 'us-east-1'
});

const ssm = new AWS.SSM({
  region: process.env.AWS_REGION || 'us-east-1'
});

/**
 * Configuration management utility
 * Handles environment variables, AWS Secrets Manager, and Parameter Store
 */
class ConfigService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Get configuration value with fallback hierarchy:
   * 1. Environment variable
   * 2. AWS Secrets Manager
   * 3. AWS Parameter Store
   * 4. Default value
   */
  async get(key, defaultValue = null, options = {}) {
    const { 
      useCache = true, 
      secretName = null, 
      parameterName = null,
      required = false 
    } = options;

    // Check cache first
    if (useCache && this.cache.has(key)) {
      const cached = this.cache.get(key);
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.value;
      }
    }

    let value = null;

    try {
      // 1. Check environment variable
      value = process.env[key];
      
      if (value) {
        logger.debug('Config value found in environment', { key });
        this.setCacheValue(key, value);
        return value;
      }

      // 2. Check AWS Secrets Manager
      if (secretName) {
        try {
          value = await this.getSecret(secretName, key);
          if (value) {
            logger.debug('Config value found in Secrets Manager', { key, secretName });
            this.setCacheValue(key, value);
            return value;
          }
        } catch (error) {
          logger.warn('Failed to get secret from Secrets Manager', { 
            key, 
            secretName, 
            error: error.message 
          });
        }
      }

      // 3. Check AWS Parameter Store
      if (parameterName) {
        try {
          value = await this.getParameter(parameterName);
          if (value) {
            logger.debug('Config value found in Parameter Store', { key, parameterName });
            this.setCacheValue(key, value);
            return value;
          }
        } catch (error) {
          logger.warn('Failed to get parameter from Parameter Store', { 
            key, 
            parameterName, 
            error: error.message 
          });
        }
      }

      // 4. Use default value
      if (defaultValue !== null) {
        logger.debug('Using default config value', { key });
        this.setCacheValue(key, defaultValue);
        return defaultValue;
      }

      // 5. Throw error if required
      if (required) {
        throw new Error(`Required configuration value not found: ${key}`);
      }

      return null;
    } catch (error) {
      logger.error('Failed to get configuration value', { key, error: error.message });
      
      if (required) {
        throw error;
      }
      
      return defaultValue;
    }
  }

  /**
   * Get secret from AWS Secrets Manager
   */
  async getSecret(secretName, key = null) {
    try {
      const result = await secretsManager.getSecretValue({ SecretId: secretName }).promise();
      
      if (result.SecretString) {
        const secrets = JSON.parse(result.SecretString);
        return key ? secrets[key] : secrets;
      }
      
      return null;
    } catch (error) {
      if (error.code === 'ResourceNotFoundException') {
        logger.warn('Secret not found', { secretName });
        return null;
      }
      throw error;
    }
  }

  /**
   * Get parameter from AWS Parameter Store
   */
  async getParameter(parameterName, decrypt = true) {
    try {
      const result = await ssm.getParameter({ 
        Name: parameterName, 
        WithDecryption: decrypt 
      }).promise();
      
      return result.Parameter.Value;
    } catch (error) {
      if (error.code === 'ParameterNotFound') {
        logger.warn('Parameter not found', { parameterName });
        return null;
      }
      throw error;
    }
  }

  /**
   * Set value in cache
   */
  setCacheValue(key, value) {
    this.cache.set(key, {
      value,
      timestamp: Date.now()
    });
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * Get database configuration
   */
  async getDatabaseConfig() {
    const stage = await this.get('STAGE', 'dev');
    const region = await this.get('AWS_REGION', 'us-east-1');
    const tablePrefix = await this.get('DYNAMODB_TABLE_PREFIX', `ai-sdr-${stage}`);

    return {
      region,
      tables: {
        users: await this.get('USERS_TABLE', `${tablePrefix}-users`),
        leads: await this.get('LEADS_TABLE', `${tablePrefix}-leads`),
        campaigns: await this.get('CAMPAIGNS_TABLE', `${tablePrefix}-campaigns`),
        jobs: await this.get('JOBS_TABLE', `${tablePrefix}-jobs`)
      }
    };
  }

  /**
   * Get authentication configuration
   */
  async getAuthConfig() {
    return {
      jwtSecret: await this.get('JWT_SECRET', null, {
        secretName: 'ai-sdr/auth',
        required: true
      }),
      jwtExpiresIn: await this.get('JWT_EXPIRES_IN', '24h'),
      bcryptRounds: parseInt(await this.get('BCRYPT_ROUNDS', '12'))
    };
  }

  /**
   * Get email configuration
   */
  async getEmailConfig() {
    return {
      sendgridApiKey: await this.get('SENDGRID_API_KEY', null, {
        secretName: 'ai-sdr/sendgrid',
        required: true
      }),
      fromEmail: await this.get('EMAIL_FROM', '<EMAIL>'),
      fromName: await this.get('EMAIL_FROM_NAME', 'AI SDR Agent')
    };
  }

  /**
   * Get SMS configuration
   */
  async getSmsConfig() {
    return {
      twilioAccountSid: await this.get('TWILIO_ACCOUNT_SID', null, {
        secretName: 'ai-sdr/twilio',
        required: true
      }),
      twilioAuthToken: await this.get('TWILIO_AUTH_TOKEN', null, {
        secretName: 'ai-sdr/twilio',
        required: true
      }),
      twilioPhoneNumber: await this.get('TWILIO_PHONE_NUMBER', null, {
        secretName: 'ai-sdr/twilio',
        required: true
      })
    };
  }

  /**
   * Get LinkedIn configuration
   */
  async getLinkedInConfig() {
    return {
      clientId: await this.get('LINKEDIN_CLIENT_ID', null, {
        secretName: 'ai-sdr/linkedin'
      }),
      clientSecret: await this.get('LINKEDIN_CLIENT_SECRET', null, {
        secretName: 'ai-sdr/linkedin'
      }),
      redirectUri: await this.get('LINKEDIN_REDIRECT_URI', null)
    };
  }

  /**
   * Get HubSpot configuration
   */
  async getHubSpotConfig() {
    return {
      accessToken: await this.get('HUBSPOT_ACCESS_TOKEN', null, {
        secretName: 'ai-sdr/hubspot'
      }),
      apiKey: await this.get('HUBSPOT_API_KEY', null, {
        secretName: 'ai-sdr/hubspot'
      })
    };
  }

  /**
   * Get all application configuration
   */
  async getAppConfig() {
    return {
      stage: await this.get('STAGE', 'dev'),
      region: await this.get('AWS_REGION', 'us-east-1'),
      logLevel: await this.get('LOG_LEVEL', 'info'),
      corsOrigin: await this.get('CORS_ORIGIN', '*'),
      database: await this.getDatabaseConfig(),
      auth: await this.getAuthConfig(),
      email: await this.getEmailConfig(),
      sms: await this.getSmsConfig(),
      linkedin: await this.getLinkedInConfig(),
      hubspot: await this.getHubSpotConfig()
    };
  }
}

// Export singleton instance
const configService = new ConfigService();

module.exports = {
  ConfigService,
  configService
};
