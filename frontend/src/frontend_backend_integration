import React, { useState, useEffect, useReducer, createContext, useContext } from 'react';
import { initializeApp } from 'firebase/app';
import { getAuth, signInAnonymously, onAuthStateChanged, signInWithCustomToken, signOut } from 'firebase/auth';
import { getFirestore, collection, onSnapshot, addDoc, doc, setDoc, deleteDoc, query } from 'firebase/firestore';

// Define the AuthContext to manage user authentication state
const AuthContext = createContext(null);

// Auth Reducer for simple state management
const authReducer = (state, action) => {
  switch (action.type) {
    case 'LOGIN':
      return { ...state, isAuthenticated: true, user: action.payload };
    case 'LOGOUT':
      return { ...state, isAuthenticated: false, user: null };
    case 'SET_AUTH_READY':
      return { ...state, isAuthReady: true };
    default:
      return state;
  }
};

// AuthProvider component to wrap the application and provide auth context
const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, {
    isAuthenticated: false,
    user: null,
    isAuthReady: false,
  });

  const [db, setDb] = useState(null);
  const [auth, setAuth] = useState(null);

  useEffect(() => {
    try {
      // MANDATORY: Initialize Firebase with __firebase_config
      const firebaseConfig = typeof __firebase_config !== 'undefined' ? JSON.parse(__firebase_config) : {};
      const app = initializeApp(firebaseConfig);
      const firestore = getFirestore(app);
      const firebaseAuth = getAuth(app);

      setDb(firestore);
      setAuth(firebaseAuth);

      // MANDATORY: Sign in with custom token or anonymously
      const signIn = async () => {
        try {
          if (typeof __initial_auth_token !== 'undefined' && __initial_auth_token) {
            await signInWithCustomToken(firebaseAuth, __initial_auth_token);
          } else {
            await signInAnonymously(firebaseAuth);
          }
        } catch (error) {
          console.error("Firebase authentication error:", error);
          // Fallback to anonymous sign-in if custom token fails
          try {
            await signInAnonymously(firebaseAuth);
          } catch (anonError) {
            console.error("Anonymous sign-in also failed:", anonError);
          }
        }
      };

      signIn();

      // Listen for authentication state changes
      const unsubscribe = onAuthStateChanged(firebaseAuth, (user) => {
        if (user) {
          dispatch({ type: 'LOGIN', payload: user });
        } else {
          dispatch({ type: 'LOGOUT' });
        }
        dispatch({ type: 'SET_AUTH_READY' });
      });

      return () => unsubscribe();
    } catch (error) {
      console.error("Failed to initialize Firebase:", error);
      dispatch({ type: 'SET_AUTH_READY' }); // Mark as ready even if init fails
    }
  }, []);

  return (
    <AuthContext.Provider value={{ ...state, dispatch, db, auth }}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use the AuthContext
const useAuth = () => {
  return useContext(AuthContext);
};

// Reusable Button component with Shadcn/UI aesthetic
const Button = ({ children, onClick, variant = 'default', className = '', ...props }) => {
  const baseStyle = 'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50';
  const variants = {
    default: 'bg-indigo-600 text-white shadow hover:bg-indigo-700',
    outline: 'border border-input bg-transparent shadow-sm hover:bg-accent hover:text-accent-foreground',
    secondary: 'bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80',
    ghost: 'hover:bg-accent hover:text-accent-foreground',
    link: 'text-primary underline-offset-4 hover:underline',
    destructive: 'bg-red-600 text-white shadow-sm hover:bg-red-700',
  };
  const sizes = {
    default: 'h-9 px-4 py-2',
    sm: 'h-8 rounded-md px-3 text-xs',
    lg: 'h-10 rounded-md px-8',
    icon: 'h-9 w-9',
  };

  return (
    <button
      className={`${baseStyle} ${variants[variant]} ${sizes.default} ${className}`}
      onClick={onClick}
      {...props}
    >
      {children}
    </button>
  );
};

// Reusable Input component with Shadcn/UI aesthetic
const Input = ({ type = 'text', placeholder = '', className = '', ...props }) => {
  return (
    <input
      type={type}
      className={`flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 ${className}`}
      placeholder={placeholder}
      {...props}
    />
  );
};

// Reusable Select component
const Select = ({ children, className = '', ...props }) => {
  return (
    <select
      className={`flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 ${className}`}
      {...props}
    >
      {children}
    </select>
  );
};

// Reusable Modal Component
const Modal = ({ isOpen, onClose, title, children }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
      <div className="relative w-full max-w-lg rounded-lg bg-white p-6 shadow-lg">
        <div className="flex items-center justify-between border-b pb-3 mb-4">
          <h3 className="text-lg font-semibold">{title}</h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            &times;
          </button>
        </div>
        <div className="modal-content">
          {children}
        </div>
      </div>
    </div>
  );
};

// Icon components (using inline SVG for simplicity and to avoid external dependencies for this example)
const UserIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
    <circle cx="12" cy="7" r="4" />
  </svg>
);

const LockIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <rect width="18" height="11" x="3" y="11" rx="2" ry="2" />
    <path d="M7 11V7a5 5 0 0 1 10 0v4" />
  </svg>
);

const DashboardIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <rect x="3" y="3" width="7" height="9" />
    <rect x="14" y="3" width="7" height="5" />
    <rect x="14" y="12" width="7" height="9" />
    <rect x="3" y="16" width="7" height="5" />
  </svg>
);

const UsersIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
    <circle cx="9" cy="7" r="4" />
    <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
    <path d="M16 3.13a4 4 0 0 1 0 7.75" />
  </svg>
);

const SortAscIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M3 10h18M3 6h18M3 14h18M3 18h18" />
  </svg>
);

const SortDescIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M3 18h18M3 14h18M3 10h18M3 6h18" />
  </svg>
);

const EditIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
  </svg>
);

const TrashIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M3 6h18" />
    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" />
  </svg>
);

const LogOutIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" />
    <polyline points="17 16 22 12 17 8" />
    <line x1="22" x2="10" y1="12" y2="12" />
  </svg>
);

const UploadIcon = (props) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
      <polyline points="17 8 12 3 7 8" />
      <line x1="12" x2="12" y1="3" y2="15" />
    </svg>
);

const DownloadIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
    <polyline points="7 10 12 15 17 10" />
    <line x1="12" x2="12" y1="15" y2="3" />
  </svg>
);

const MailIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <rect width="20" height="16" x="2" y="4" rx="2" />
    <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
  </svg>
);


// LoginPage component
const LoginPage = ({ onLoginSuccess }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const { auth, dispatch, isAuthReady } = useAuth();

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    if (!isAuthReady) {
      setError("Authentication service not ready. Please wait.");
      setLoading(false);
      return;
    }

    if (username && password) {
      try {
        if (auth.currentUser) {
          dispatch({ type: 'LOGIN', payload: auth.currentUser });
          onLoginSuccess();
        } else {
          setError("Failed to get authenticated user. Please try again.");
          await signInAnonymously(auth);
          if (auth.currentUser) {
            dispatch({ type: 'LOGIN', payload: auth.currentUser });
            onLoginSuccess();
          } else {
            setError("Authentication failed.");
          }
        }
      } catch (err) {
        console.error("Login error:", err);
        setError("Login failed. Please check your credentials.");
      } finally {
        setLoading(false);
      }
    } else {
      setError('Please enter both username and password.');
      setLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-100 p-4">
      <div className="w-full max-w-md space-y-8 rounded-lg bg-white p-8 shadow-xl">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Sign in to AI SDR Agent
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Automate your mining equipment sales development
          </p>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleLogin}>
          {error && (
            <div className="rounded-md bg-red-50 p-3 text-sm text-red-700">
              {error}
            </div>
          )}
          <div className="rounded-md shadow-sm -space-y-px">
            <div>
              <label htmlFor="username" className="sr-only">
                Username
              </label>
              <div className="relative">
                <Input
                  id="username"
                  name="username"
                  type="text"
                  autoComplete="username"
                  required
                  className="relative block w-full rounded-t-md border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                  placeholder="Username"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                />
                <UserIcon className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400" />
              </div>
            </div>
            <div>
              <label htmlFor="password" className="sr-only">
                Password
              </label>
              <div className="relative">
                <Input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  required
                  className="relative block w-full rounded-b-md border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm mt-px"
                  placeholder="Password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
                <LockIcon className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400" />
              </div>
            </div>
          </div>

          <div>
            <Button
              type="submit"
              className="group relative flex w-full justify-center"
              disabled={loading || !isAuthReady}
            >
              {loading ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 1116 0A8 8 0 014 12z"></path>
                  </svg>
                  Signing in...
                </span>
              ) : (
                'Sign In'
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

// DashboardPage component
const DashboardPage = ({ onNavigate, user }) => {
  const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
  const { auth, dispatch } = useAuth();

  const [dashboardMetrics, setDashboardMetrics] = useState({
    totalLeads: 'N/A',
    emailsSent: 'N/A',
    conversionRate: 'N/A',
  });
  const [metricsLoading, setMetricsLoading] = useState(true);
  const [metricsError, setMetricsError] = useState('');

  // Backend API URL for fetching analytics metrics
  // IMPORTANT: Replace with your actual API Gateway endpoint
  const API_GATEWAY_ANALYTICS_URL = process.env.REACT_APP_API_GATEWAY_ANALYTICS_URL || 'https://your-api-gateway-id.execute-api.us-east-1.amazonaws.com/prod/analytics/dashboard-metrics';


  useEffect(() => {
    const fetchMetrics = async () => {
      setMetricsLoading(true);
      setMetricsError('');
      try {
        const response = await fetch(API_GATEWAY_ANALYTICS_URL, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            // Add authorization header if your API Gateway uses an authorizer
            // 'Authorization': `Bearer ${await auth.currentUser.getIdToken()}`,
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        setDashboardMetrics({
          totalLeads: data.totalLeads !== undefined ? data.totalLeads : 'N/A',
          emailsSent: data.emailsSent !== undefined ? data.emailsSent : 'N/A',
          conversionRate: data.conversionRate !== undefined ? `${data.conversionRate}%` : 'N/A',
        });
      } catch (error) {
        console.error("Error fetching dashboard metrics:", error);
        setMetricsError("Failed to load dashboard metrics. Ensure backend is running.");
      } finally {
        setMetricsLoading(false);
      }
    };

    // Fetch metrics only if the user is authenticated (to avoid unnecessary calls before auth is ready)
    if (auth && auth.currentUser) {
      fetchMetrics();
    }
    // No dependency on 'auth.currentUser' directly in useEffect array to prevent re-runs on token refresh,
    // instead check inside the effect. 'auth' is stable after init.
  }, [auth]); // Depend on auth instance, then check currentUser inside.


  const handleLogout = async () => {
    try {
      await signOut(auth);
      dispatch({ type: 'LOGOUT' });
      onNavigate('login');
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };


  return (
    <div className="flex h-screen bg-gray-50 text-gray-900 font-inter">
      {/* Sidebar Navigation */}
      <aside className="w-64 bg-white p-6 shadow-md rounded-r-lg flex flex-col justify-between">
        <div>
          <div className="text-2xl font-bold text-indigo-700 mb-8">AI SDR Agent</div>
          <nav className="space-y-4">
            <Button variant="ghost" className="w-full justify-start text-lg px-3 py-2 rounded-lg hover:bg-gray-100 flex items-center gap-2" onClick={() => onNavigate('dashboard')}>
              <DashboardIcon className="h-5 w-5" /> Dashboard
            </Button>
            <Button variant="ghost" className="w-full justify-start text-lg px-3 py-2 rounded-lg hover:bg-gray-100 flex items-center gap-2" onClick={() => onNavigate('leads')}>
              <UsersIcon className="h-5 w-5" /> Leads
            </Button>
            <Button variant="ghost" className="w-full justify-start text-lg px-3 py-2 rounded-lg hover:bg-gray-100 flex items-center gap-2" onClick={() => onNavigate('outreach')}>
              <MailIcon className="h-5 w-5" /> Outreach
            </Button>
            <Button variant="ghost" className="w-full justify-start text-lg px-3 py-2 rounded-lg hover:bg-gray-100 flex items-center gap-2" onClick={() => alert('Feature Coming Soon: Analytics & Reporting!')}>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5"><path d="M12 20V10"/><path d="M18 20V4"/><path d="M6 20v-4"/></svg> Analytics
            </Button>
          </nav>
        </div>
        <div className="text-sm text-gray-500">
          <p className="mb-2">Logged in as: <span className="font-medium text-gray-700">{user?.email || 'Anonymous'}</span></p>
          <p className="break-all">User ID: <span className="font-medium text-gray-700">{user?.uid || 'N/A'}</span></p>
          <p className="mt-1">App ID: <span className="font-medium text-gray-700">{appId}</span></p>
          <Button variant="ghost" className="w-full justify-start text-lg px-3 py-2 rounded-lg hover:bg-gray-100 flex items-center gap-2 mt-4" onClick={handleLogout}>
            <LogOutIcon className="h-5 w-5" /> Logout
          </Button>
        </div>
      </aside>

      {/* Main Content Area */}
      <main className="flex-1 p-8 overflow-auto">
        <header className="mb-8">
          <h1 className="text-4xl font-bold text-gray-800">Welcome to Your Dashboard!</h1>
          <p className="text-gray-600 mt-2">Get started by managing your leads or reviewing analytics.</p>
        </header>

        {metricsError && (
            <div className="rounded-md bg-red-50 p-3 text-sm text-red-700 mb-4">
                {metricsError}
            </div>
        )}

        <section className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Card 1: Total Leads */}
          <div className="rounded-lg bg-white p-6 shadow-md border border-gray-200">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-gray-800">Total Leads</h3>
              <UsersIcon className="h-7 w-7 text-indigo-500" />
            </div>
            <p className="text-5xl font-bold text-gray-900">
                {metricsLoading ? <span className="animate-pulse">...</span> : dashboardMetrics.totalLeads}
            </p>
            <p className="text-sm text-gray-500 mt-2">+15% from last month</p>
          </div>

          {/* Card 2: Emails Sent */}
          <div className="rounded-lg bg-white p-6 shadow-md border border-gray-200">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-gray-800">Emails Sent</h3>
              <MailIcon className="h-7 w-7 text-green-500"/>
            </div>
            <p className="text-5xl font-bold text-gray-900">
                {metricsLoading ? <span className="animate-pulse">...</span> : dashboardMetrics.emailsSent}
            </p>
            <p className="text-sm text-gray-500 mt-2">+10% from last month</p>
          </div>

          {/* Card 3: Conversion Rate */}
          <div className="rounded-lg bg-white p-6 shadow-md border border-gray-200">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-gray-800">Conversion Rate</h3>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-7 w-7 text-yellow-500"><polyline points="22 12 18 12 15 21 9 3 6 12 2 12"/></svg>
            </div>
            <p className="text-5xl font-bold text-gray-900">
                {metricsLoading ? <span className="animate-pulse">...</span> : dashboardMetrics.conversionRate}
            </p>
            <p className="text-sm text-gray-500 mt-2">Target: 5.0%</p>
          </div>
        </section>

        <section className="mt-8 bg-white p-6 shadow-md rounded-lg border border-gray-200">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Quick Actions</h2>
          <div className="flex flex-wrap gap-4">
            <Button variant="default" className="text-base px-6 py-3" onClick={() => alert('Triggering AI Lead Generation... (Backend Integration needed)')}>Generate New Leads</Button>
            <Button variant="outline" className="text-base px-6 py-3">Review High-Priority Leads</Button>
            <Button variant="outline" className="text-base px-6 py-3" onClick={() => onNavigate('outreach')}>Schedule Outreach Campaign</Button>
          </div>
        </section>
      </main>
    </div>
  );
};

// LeadsListPage component
const LeadsListPage = ({ user }) => {
  const { db, auth } = useAuth();
  const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
  const userId = auth?.currentUser?.uid || 'anonymous_user';

  const [leads, setLeads] = useState([]);
  const [loadingLeads, setLoadingLeads] = useState(true);
  const [leadError, setLeadError] = useState('');

  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('All');
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'ascending' });
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingLead, setEditingLead] = useState(null);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [leadToDelete, setLeadToDelete] = useState(null);
  const [isAddLeadModalOpen, setIsAddLeadModalOpen] = useState(false);
  const [newLead, setNewLead] = useState({ companyName: '', score: 0, status: 'New', lastContacted: '' });
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [importFile, setImportFile] = useState(null);
  const [importError, setImportError] = useState('');
  const [importLoading, setImportLoading] = useState(false);


  // Backend API URL for pre-signed S3 URL
  // IMPORTANT: Replace with your actual API Gateway endpoint
  const API_GATEWAY_PRESIGNED_URL = 'https://your-api-gateway-id.execute-api.us-east-1.amazonaws.com/prod/leads/import-initiate';


  // Firestore Collection Path
  const leadsCollectionPath = `artifacts/${appId}/users/${userId}/leads`;

  // Fetch leads from Firestore in real-time
  useEffect(() => {
    if (!db || !user) {
      setLoadingLeads(false);
      return;
    }

    setLoadingLeads(true);
    setLeadError('');
    try {
      const q = query(collection(db, leadsCollectionPath));
      const unsubscribe = onSnapshot(q, (snapshot) => {
        const fetchedLeads = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        setLeads(fetchedLeads);
        setLoadingLeads(false);
      }, (error) => {
        console.error("Error fetching leads:", error);
        setLeadError("Failed to load leads. Please try again.");
        setLoadingLeads(false);
      });

      // Cleanup subscription on component unmount
      return () => unsubscribe();
    } catch (error) {
      console.error("Error setting up leads listener:", error);
      setLeadError("Failed to initialize leads data. Check console for details.");
      setLoadingLeads(false);
    }
  }, [db, user, leadsCollectionPath]);


  const filteredLeads = leads.filter(lead => {
    const matchesSearch = lead.companyName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'All' || lead.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  const sortedLeads = React.useMemo(() => {
    let sortableLeads = [...filteredLeads];
    if (sortConfig.key) {
      // Basic sorting for strings and numbers
      sortableLeads.sort((a, b) => {
        const aValue = a[sortConfig.key];
        const bValue = b[sortConfig.key];

        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return sortConfig.direction === 'ascending' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
        } else {
          // For numbers and dates (assuming date strings are comparable or can be converted)
          if (aValue < bValue) {
            return sortConfig.direction === 'ascending' ? -1 : 1;
          }
          if (aValue > bValue) {
            return sortConfig.direction === 'ascending' ? 1 : -1;
          }
          return 0;
        }
      });
    }
    return sortableLeads;
  }, [filteredLeads, sortConfig]);

  const requestSort = (key) => {
    let direction = 'ascending';
    if (sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };

  const handleEditClick = (lead) => {
    setEditingLead({ ...lead }); // Create a copy to edit
    setIsEditModalOpen(true);
  };

  const handleSaveLead = async (updatedLead) => {
    if (!db || !user) {
      setLeadError("Database not available. Cannot save lead.");
      return;
    }
    try {
      // Update doc in Firestore
      await setDoc(doc(db, leadsCollectionPath, updatedLead.id), updatedLead);
      setIsEditModalOpen(false);
      setEditingLead(null);
      setLeadError(''); // Clear any previous errors
    } catch (error) {
      console.error("Error updating lead:", error);
      setLeadError("Failed to save lead changes. Please try again.");
    }
  };

  const handleDeleteClick = (lead) => {
    setLeadToDelete(lead);
    setIsConfirmModalOpen(true);
  };

  const confirmDelete = async () => {
    if (!db || !user || !leadToDelete) {
      setLeadError("Database not available or lead to delete is missing.");
      return;
    }
    try {
      // Delete doc from Firestore
      await deleteDoc(doc(db, leadsCollectionPath, leadToDelete.id));
      setIsConfirmModalOpen(false);
      setLeadToDelete(null);
      setLeadError(''); // Clear any previous errors
    } catch (error) {
      console.error("Error deleting lead:", error);
      setLeadError("Failed to delete lead. Please try again.");
    }
  };

  const cancelDelete = () => {
    setIsConfirmModalOpen(false);
    setLeadToDelete(null);
  };

  const handleAddLeadClick = () => {
    setNewLead({ companyName: '', score: 0, status: 'New', lastContacted: new Date().toISOString().split('T')[0] }); // Pre-fill with current date
    setIsAddLeadModalOpen(true);
  };

  const handleCreateNewLead = async (e) => {
    e.preventDefault();
    if (!db || !user) {
      setLeadError("Database not available. Cannot add new lead.");
      return;
    }
    try {
      // Add a new doc to Firestore
      await addDoc(collection(db, leadsCollectionPath), newLead);
      setIsAddLeadModalOpen(false);
      setNewLead({ companyName: '', score: 0, status: 'New', lastContacted: '' });
      setLeadError(''); // Clear any previous errors
    } catch (error) {
      console.error("Error adding new lead:", error);
      setLeadError("Failed to add new lead. Please try again.");
    }
  };

  const handleFileChange = (event) => {
    setImportFile(event.target.files[0]);
    setImportError('');
  };

  const handleImportLeads = async () => {
    if (!importFile) {
      setImportError("Please select a CSV file to import.");
      return;
    }
    setImportLoading(true);
    setImportError('');

    try {
      // 1. Get pre-signed S3 URL from backend
      const getPresignedUrlResponse = await fetch(API_GATEWAY_PRESIGNED_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Add authorization header if your API Gateway uses an authorizer
          // 'Authorization': `Bearer ${await auth.currentUser.getIdToken()}`,
        },
        body: JSON.stringify({
          fileName: importFile.name,
          contentType: importFile.type,
          userId: userId, // Pass userId for backend path construction
          appId: appId // Pass appId for backend path construction
        })
      });

      if (!getPresignedUrlResponse.ok) {
        throw new Error(`Failed to get pre-signed URL: ${getPresignedUrlResponse.statusText}`);
      }

      const { uploadUrl, fileKey } = await getPresignedUrlResponse.json();

      // 2. Upload file directly to S3 using the pre-signed URL
      const uploadResponse = await fetch(uploadUrl, {
        method: 'PUT',
        body: importFile,
        headers: {
          'Content-Type': importFile.type,
        },
      });

      if (!uploadResponse.ok) {
        throw new Error(`Failed to upload file to S3: ${uploadResponse.statusText}`);
      }

      alert('CSV file uploaded to S3 successfully! Leads will be processed in the backend.');
      setIsImportModalOpen(false);
      setImportFile(null);
    } catch (error) {
      console.error("Error during import process:", error);
      setImportError(`Import failed: ${error.message}. Please try again.`);
    } finally {
      setImportLoading(false);
    }
  };

  const handleExportLeads = () => {
    if (leads.length === 0) {
      alert("No leads to export.");
      return;
    }

    const headers = ["id", "companyName", "score", "status", "lastContacted"];
    const csvContent = [
      headers.join(','),
      ...leads.map(lead => headers.map(header => {
          // Ensure no commas or newlines break the CSV format
          const value = String(lead[header] || '').replace(/"/g, '""');
          return `"${value}"`; // Quote all fields to handle commas within data
      }).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    if (link.download !== undefined) { // feature detection
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `ai_sdr_leads_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    } else {
        alert("Your browser does not support downloading files directly. Please copy the data manually from console.");
        console.log(csvContent); // Log to console as fallback
    }
  };


  return (
    <div className="flex h-screen bg-gray-50 text-gray-900 font-inter">
      <main className="flex-1 p-8 overflow-auto">
        <header className="mb-8">
          <h1 className="text-4xl font-bold text-gray-800">Leads List</h1>
          <p className="text-gray-600 mt-2">View, filter, sort, and manage your potential customers.</p>
        </header>

        <section className="bg-white p-6 shadow-md rounded-lg border border-gray-200">
          <div className="mb-4 flex flex-col sm:flex-row items-center justify-between gap-4">
            <Input
              type="search"
              placeholder="Search leads by company name..."
              className="max-w-xs w-full"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <div className="flex items-center gap-2 w-full sm:w-auto">
              <label htmlFor="status-filter" className="text-sm font-medium text-gray-700 whitespace-nowrap">Filter by Status:</label>
              <Select
                id="status-filter"
                className="flex-grow sm:flex-none"
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <option value="All">All</option>
                <option value="New">New</option>
                <option value="Contacted">Contacted</option>
                <option value="Qualified">Qualified</option>
                <option value="Disqualified">Disqualified</option>
              </Select>
            </div>
            <div className="flex flex-wrap gap-2 justify-end w-full sm:w-auto">
              <Button variant="outline" onClick={() => setIsImportModalOpen(true)} disabled={importLoading}>
                {importLoading ? <svg className="animate-spin h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 1116 0A8 8 0 014 12z"></path></svg> : <UploadIcon className="h-4 w-4 mr-1" />} Import Leads
              </Button>
              <Button variant="outline" onClick={handleExportLeads}>
                <DownloadIcon className="h-4 w-4 mr-1" /> Export Leads
              </Button>
              <Button variant="default" onClick={handleAddLeadClick}>Add New Lead</Button>
            </div>
          </div>

          {leadError && (
            <div className="rounded-md bg-red-50 p-3 text-sm text-red-700 mb-4">
              {leadError}
            </div>
          )}

          {loadingLeads ? (
            <div className="flex justify-center items-center py-8">
              <svg className="animate-spin h-8 w-8 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 1116 0A8 8 0 014 12z"></path>
              </svg>
              <p className="ml-3 text-gray-700">Loading leads...</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 rounded-md overflow-hidden">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onClick={() => requestSort('companyName')}>
                      Company Name
                      {sortConfig.key === 'companyName' && (sortConfig.direction === 'ascending' ? <SortAscIcon className="inline-block ml-1 h-4 w-4" /> : <SortDescIcon className="inline-block ml-1 h-4 w-4" />)}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onClick={() => requestSort('score')}>
                      Score
                      {sortConfig.key === 'score' && (sortConfig.direction === 'ascending' ? <SortAscIcon className="inline-block ml-1 h-4 w-4" /> : <SortDescIcon className="inline-block ml-1 h-4 w-4" />)}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onClick={() => requestSort('lastContacted')}>
                      Last Contacted
                      {sortConfig.key === 'lastContacted' && (sortConfig.direction === 'ascending' ? <SortAscIcon className="inline-block ml-1 h-4 w-4" /> : <SortDescIcon className="inline-block ml-1 h-4 w-4" />)}
                    </th>
                    <th scope="col" className="relative px-6 py-3">
                      <span className="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {sortedLeads.map((lead) => (
                    <tr key={lead.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {lead.companyName}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          lead.score >= 80 ? 'bg-green-100 text-green-800' :
                          lead.score >= 60 ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {lead.score}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {lead.status}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {lead.lastContacted}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          <Button variant="outline" size="sm" onClick={() => handleEditClick(lead)}>
                            <EditIcon className="h-4 w-4 mr-1" /> Edit
                          </Button>
                          <Button variant="destructive" size="sm" onClick={() => handleDeleteClick(lead)}>
                            <TrashIcon className="h-4 w-4 mr-1" /> Delete
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {sortedLeads.length === 0 && (
                  <p className="text-center py-8 text-gray-500">No leads found matching your criteria.</p>
              )}
            </div>
          )}
        </section>

        {/* Edit Lead Modal */}
        {editingLead && (
            <Modal isOpen={isEditModalOpen} onClose={() => setIsEditModalOpen(false)} title="Edit Lead">
                <form onSubmit={(e) => {
                    e.preventDefault();
                    handleSaveLead(editingLead);
                }}>
                    <div className="grid grid-cols-1 gap-4 mb-6">
                        <div>
                            <label htmlFor="companyName" className="block text-sm font-medium text-gray-700">Company Name</label>
                            <Input
                                id="companyName"
                                value={editingLead.companyName}
                                onChange={(e) => setEditingLead({ ...editingLead, companyName: e.target.value })}
                                className="mt-1"
                            />
                        </div>
                        <div>
                            <label htmlFor="score" className="block text-sm font-medium text-gray-700">Score</label>
                            <Input
                                id="score"
                                type="number"
                                value={editingLead.score}
                                onChange={(e) => setEditingLead({ ...editingLead, score: parseInt(e.target.value) || 0 })}
                                className="mt-1"
                            />
                        </div>
                        <div>
                            <label htmlFor="status" className="block text-sm font-medium text-gray-700">Status</label>
                            <Select
                                id="status"
                                value={editingLead.status}
                                onChange={(e) => setEditingLead({ ...editingLead, status: e.target.value })}
                                className="mt-1"
                            >
                                <option value="New">New</option>
                                <option value="Contacted">Contacted</option>
                                <option value="Qualified">Qualified</option>
                                <option value="Disqualified">Disqualified</option>
                            </Select>
                        </div>
                        <div>
                            <label htmlFor="lastContacted" className="block text-sm font-medium text-gray-700">Last Contacted</label>
                            <Input
                                id="lastContacted"
                                type="date"
                                value={editingLead.lastContacted}
                                onChange={(e) => setEditingLead({ ...editingLead, lastContacted: e.target.value })}
                                className="mt-1"
                            />
                        </div>
                    </div>
                    <div className="flex justify-end space-x-2">
                        <Button type="button" variant="outline" onClick={() => setIsEditModalOpen(false)}>Cancel</Button>
                        <Button type="submit">Save Changes</Button>
                    </div>
                </form>
            </Modal>
        )}

        {/* Add New Lead Modal */}
        {newLead && (
            <Modal isOpen={isAddLeadModalOpen} onClose={() => setIsAddLeadModalOpen(false)} title="Add New Lead">
                <form onSubmit={handleCreateNewLead}>
                    <div className="grid grid-cols-1 gap-4 mb-6">
                        <div>
                            <label htmlFor="newCompanyName" className="block text-sm font-medium text-gray-700">Company Name</label>
                            <Input
                                id="newCompanyName"
                                value={newLead.companyName}
                                onChange={(e) => setNewLead({ ...newLead, companyName: e.target.value })}
                                className="mt-1"
                                required
                            />
                        </div>
                        <div>
                            <label htmlFor="newScore" className="block text-sm font-medium text-gray-700">Score</label>
                            <Input
                                id="newScore"
                                type="number"
                                value={newLead.score}
                                onChange={(e) => setNewLead({ ...newLead, score: parseInt(e.target.value) || 0 })}
                                className="mt-1"
                            />
                        </div>
                        <div>
                            <label htmlFor="newStatus" className="block text-sm font-medium text-gray-700">Status</label>
                            <Select
                                id="newStatus"
                                value={newLead.status}
                                onChange={(e) => setNewLead({ ...newLead, status: e.target.value })}
                                className="mt-1"
                            >
                                <option value="New">New</option>
                                <option value="Contacted">Contacted</option>
                                <option value="Qualified">Qualified</option>
                                <option value="Disqualified">Disqualified</option>
                            </Select>
                        </div>
                        <div>
                            <label htmlFor="newLastContacted" className="block text-sm font-medium text-gray-700">Last Contacted</label>
                            <Input
                                id="newLastContacted"
                                type="date"
                                value={newLead.lastContacted}
                                onChange={(e) => setNewLead({ ...newLead, lastContacted: e.target.value })}
                                className="mt-1"
                            />
                        </div>
                    </div>
                    <div className="flex justify-end space-x-2">
                        <Button type="button" variant="outline" onClick={() => setIsAddLeadModalOpen(false)}>Cancel</Button>
                        <Button type="submit">Add Lead</Button>
                    </div>
                </form>
            </Modal>
        )}

        {/* Confirmation Modal for Delete */}
        <Modal isOpen={isConfirmModalOpen} onClose={cancelDelete} title="Confirm Deletion">
          <p className="text-gray-700 mb-6">Are you sure you want to delete lead for "{leadToDelete?.companyName}"? This action cannot be undone.</p>
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={cancelDelete}>Cancel</Button>
            <Button type="button" variant="destructive" onClick={confirmDelete}>Delete</Button>
          </div>
        </Modal>

        {/* Import Leads Modal */}
        <Modal isOpen={isImportModalOpen} onClose={() => setIsImportModalOpen(false)} title="Import Leads (CSV)">
          <div className="mb-4">
            <label htmlFor="csvFile" className="block text-sm font-medium text-gray-700 mb-2">Select CSV File:</label>
            <Input
              id="csvFile"
              type="file"
              accept=".csv"
              onChange={handleFileChange}
              className="w-full"
            />
            {importError && <p className="text-red-500 text-sm mt-2">{importError}</p>}
            <p className="text-gray-500 text-xs mt-2">
              Expected CSV format: <code>companyName,score,status,lastContacted</code> (headers must match).
            </p>
          </div>
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={() => setIsImportModalOpen(false)}>Cancel</Button>
            <Button type="button" onClick={handleImportLeads} disabled={!importFile || importLoading}>
                {importLoading ? <svg className="animate-spin h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 1116 0A8 8 0 014 12z"></path></svg> : <UploadIcon className="h-4 w-4 mr-1" />} Import
            </Button>
          </div>
        </Modal>

      </main>
    </div>
  );
};

// OutreachPage Component
const OutreachPage = () => {
    const [prompt, setPrompt] = useState('');
    const [generatedMessage, setGeneratedMessage] = useState('');
    const [loadingGeneration, setLoadingGeneration] = useState(false);
    const [generationError, setGenerationError] = useState('');
    const [selectedLeadId, setSelectedLeadId] = useState('');
    const [sendingEmail, setSendingEmail] = useState(false);
    const [sendEmailError, setSendEmailError] = useState('');

    const { db, auth } = useAuth();
    const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
    const userId = auth?.currentUser?.uid || 'anonymous_user';
    const leadsCollectionPath = `artifacts/${appId}/users/${userId}/leads`;

    // Backend API URLs for Outreach features
    // IMPORTANT: Replace with your actual API Gateway endpoints
    const API_GATEWAY_GENERATE_MESSAGE_URL = 'https://your-api-gateway-id.execute-api.us-east-1.amazonaws.com/prod/outreach/generate-message';
    const API_GATEWAY_SEND_EMAIL_URL = 'https://your-api-gateway-id.execute-api.us-east-1.amazonaws.com/prod/outreach/send-email';

    const [leads, setLeads] = useState([]);
    useEffect(() => {
        if (!db || !auth.currentUser) return;
        const unsubscribe = onSnapshot(collection(db, leadsCollectionPath), (snapshot) => {
            setLeads(snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })));
        });
        return () => unsubscribe();
    }, [db, auth, leadsCollectionPath]);

    const handleGenerateMessage = async () => {
        setLoadingGeneration(true);
        setGenerationError('');
        setGeneratedMessage('');

        if (!prompt || !selectedLeadId) {
            setGenerationError('Please enter a prompt and select a lead.');
            setLoadingGeneration(false);
            return;
        }

        const selectedLead = leads.find(lead => lead.id === selectedLeadId);
        if (!selectedLead) {
            setGenerationError('Selected lead not found.');
            setLoadingGeneration(false);
            return;
        }

        try {
            const response = await fetch(API_GATEWAY_GENERATE_MESSAGE_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    // Add authorization header if your API Gateway uses an authorizer
                    // 'Authorization': `Bearer ${await auth.currentUser.getIdToken()}`,
                },
                body: JSON.stringify({
                    leadId: selectedLead.id,
                    prompt: prompt,
                    // Assuming recipientEmail would be derived from lead data or a new input field
                    recipientEmail: selectedLead.email || '<EMAIL>' // Placeholder email
                })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Backend error: ${errorData.error || response.statusText}`);
            }

            const result = await response.json();
            setGeneratedMessage(result.generatedMessage);

        } catch (error) {
            console.error("Error generating message:", error);
            setGenerationError(`Failed to generate message: ${error.message}. Please check backend logs.`);
        } finally {
            setLoadingGeneration(false);
        }
    };

    const handleSendMessage = async () => {
        setSendingEmail(true);
        setSendEmailError('');

        if (!generatedMessage || !selectedLeadId) {
            setSendEmailError('Please generate a message and select a lead first!');
            setSendingEmail(false);
            return;
        }

        const selectedLead = leads.find(lead => lead.id === selectedLeadId);
        if (!selectedLead) {
            setSendEmailError('Selected lead not found for sending email.');
            setSendingEmail(false);
            return;
        }

        // Extract subject and body from the generated message
        const messageLines = generatedMessage.split('\n');
        let subject = 'No Subject';
        let messageBody = generatedMessage;

        if (messageLines[0].toLowerCase().startsWith('subject:')) {
            subject = messageLines[0].substring('subject:'.length).trim();
            messageBody = messageLines.slice(1).join('\n').trim(); // Rest of the message is body
        }


        try {
            const response = await fetch(API_GATEWAY_SEND_EMAIL_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    // Add authorization header
                    // 'Authorization': `Bearer ${await auth.currentUser.getIdToken()}`,
                },
                body: JSON.stringify({
                    leadId: selectedLead.id,
                    recipientEmail: selectedLead.email || '<EMAIL>', // Use actual lead email
                    subject: subject,
                    messageBody: messageBody,
                })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Backend error: ${errorData.error || response.statusText}`);
            }

            alert('Email enqueued successfully! It will be sent shortly.');
            setGeneratedMessage(''); // Clear message after sending
            setPrompt(''); // Clear prompt
            setSelectedLeadId(''); // Clear selected lead

        }
        catch (error) {
            console.error("Error sending message:", error);
            setSendEmailError(`Failed to enqueue email: ${error.message}.`);
        } finally {
            setSendingEmail(false);
        }
    };

    return (
        <main className="flex-1 p-8 overflow-auto">
            <header className="mb-8">
                <h1 className="text-4xl font-bold text-gray-800">Outreach Management</h1>
                <p className="text-gray-600 mt-2">Generate personalized messages and manage outreach sequences.</p>
            </header>

            <section className="bg-white p-6 shadow-md rounded-lg border border-gray-200 mb-8">
                <h2 className="text-2xl font-bold text-gray-800 mb-4">Message Generator (AI-Powered)</h2>
                <p className="text-gray-600 mb-4">Craft personalized outreach messages using AI, leveraging lead data.</p>
                {generationError && (
                    <div className="rounded-md bg-red-50 p-3 text-sm text-red-700 mb-4">
                        {generationError}
                    </div>
                )}
                {sendEmailError && (
                    <div className="rounded-md bg-red-50 p-3 text-sm text-red-700 mb-4">
                        {sendEmailError}
                    </div>
                )}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label htmlFor="lead-select" className="block text-sm font-medium text-gray-700 mb-1">Select Lead:</label>
                        <Select id="lead-select" value={selectedLeadId} onChange={(e) => setSelectedLeadId(e.target.value)} className="mb-4">
                            <option value="">-- Select a Lead --</option>
                            {leads.map(lead => (
                                <option key={lead.id} value={lead.id}>{lead.companyName} (Score: {lead.score})</option>
                            ))}
                        </Select>
                        <label htmlFor="prompt-input" className="block text-sm font-medium text-gray-700 mb-1">Prompt for AI:</label>
                        <textarea
                            id="prompt-input"
                            className="flex w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 min-h-[100px]"
                            placeholder="e.g., Mention recent mining industry trends and how our new drill reduces operational costs."
                            value={prompt}
                            onChange={(e) => setPrompt(e.target.value)}
                        ></textarea>
                        <Button onClick={handleGenerateMessage} disabled={loadingGeneration || !selectedLeadId || !prompt} className="mt-4 w-full">
                            {loadingGeneration ? (
                                <span className="flex items-center">
                                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 1116 0A8 8 0 014 12z"></path>
                                    </svg>
                                    Generating...
                                </span>
                            ) : 'Generate Message'}
                        </Button>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Generated Message:</label>
                        <textarea
                            className="flex w-full rounded-md border border-input bg-gray-50 px-3 py-2 text-sm shadow-sm min-h-[200px]"
                            readOnly
                            value={generatedMessage}
                            placeholder="AI-generated message will appear here..."
                        ></textarea>
                        <Button onClick={handleSendMessage} disabled={!generatedMessage || sendingEmail} className="mt-4 w-full bg-green-600 hover:bg-green-700">
                            {sendingEmail ? (
                                <span className="flex items-center">
                                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 1116 0A8 8 0 014 12z"></path>
                                    </svg>
                                    Sending...
                                </span>
                            ) : 'Send Message'}
                        </Button>
                    </div>
                </div>
            </section>

            <section className="bg-white p-6 shadow-md rounded-lg border border-gray-200">
                <h2 className="text-2xl font-bold text-gray-800 mb-4">Outreach Sequences</h2>
                <p className="text-gray-600 mb-4">Define and manage automated follow-up sequences for your leads.</p>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
                        <h3 className="font-semibold text-lg mb-2">Initial Contact Sequence</h3>
                        <p className="text-sm text-gray-600">3-step sequence: Intro Email, Follow-up 1 (3 days), Follow-up 2 (7 days).</p>
                        <Button variant="outline" size="sm" className="mt-3" onClick={() => alert('Sequence Management UI Coming Soon!')}>View/Edit</Button>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
                        <h3 className="font-semibold text-lg mb-2">Qualified Lead Nurture</h3>
                        <p className="text-sm text-gray-600">5-step sequence: Case Study, Product Deep Dive, Webinar Invite, Success Story, Call to Action.</p>
                        <Button variant="outline" size="sm" className="mt-3" onClick={() => alert('Sequence Management UI Coming Soon!')}>View/Edit</Button>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
                        <h3 className="font-semibold text-lg mb-2">Event Follow-up</h3>
                        <p className="text-sm text-gray-600">2-step sequence: Thanks for Meeting, Resource Sharing.</p>
                        <Button variant="outline" size="sm" className="mt-3" onClick={() => alert('Sequence Management UI Coming Soon!')}>View/Edit</Button>
                    </div>
                </div>
                <Button variant="default" className="mt-6" onClick={() => alert('Create New Sequence UI Coming Soon!')}>Create New Sequence</Button>
            </section>
        </main>
    );
};


// Main App component for routing
const App = () => {
  const [currentPage, setCurrentPage] = useState('login');
  const { isAuthenticated, isAuthReady, user } = useAuth();

  useEffect(() => {
    if (isAuthReady && isAuthenticated && currentPage === 'login') {
      setCurrentPage('dashboard');
    } else if (isAuthReady && !isAuthenticated && currentPage !== 'login') {
      setCurrentPage('login');
    }
  }, [isAuthenticated, isAuthReady, currentPage]);

  const navigateTo = (page) => {
    setCurrentPage(page);
  };

  if (!isAuthReady) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-100">
        <div className="flex items-center text-indigo-700 text-lg">
          <svg className="animate-spin -ml-1 mr-3 h-8 w-8" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 1116 0A8 8 0 014 12z"></path>
          </svg>
          Loading application...
        </div>
      </div>
    );
  }

  switch (currentPage) {
    case 'login':
      return <LoginPage onLoginSuccess={() => navigateTo('dashboard')} />;
    case 'dashboard':
      return <DashboardPage onNavigate={navigateTo} user={user} />;
    case 'leads':
      return (
        <div className="flex h-screen bg-gray-50">
          <aside className="w-64 bg-white p-6 shadow-md rounded-r-lg flex flex-col justify-between">
            <div>
              <div className="text-2xl font-bold text-indigo-700 mb-8">AI SDR Agent</div>
              <nav className="space-y-4">
                <Button variant="ghost" className="w-full justify-start text-lg px-3 py-2 rounded-lg hover:bg-gray-100 flex items-center gap-2" onClick={() => navigateTo('dashboard')}>
                  <DashboardIcon className="h-5 w-5" /> Dashboard
                </Button>
                <Button variant="ghost" className="w-full justify-start text-lg px-3 py-2 rounded-lg hover:bg-gray-100 flex items-center gap-2" onClick={() => navigateTo('leads')}>
                  <UsersIcon className="h-5 w-5" /> Leads
                </Button>
                <Button variant="ghost" className="w-full justify-start text-lg px-3 py-2 rounded-lg hover:bg-gray-100 flex items-center gap-2" onClick={() => navigateTo('outreach')}>
                  <MailIcon className="h-5 w-5" /> Outreach
                </Button>
                <Button variant="ghost" className="w-full justify-start text-lg px-3 py-2 rounded-lg hover:bg-gray-100 flex items-center gap-2" onClick={() => alert('Feature Coming Soon: Analytics & Reporting!')}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5"><path d="M12 20V10"/><path d="M18 20V4"/><path d="M6 20v-4"/></svg> Analytics
                </Button>
              </nav>
            </div>
            <div className="text-sm text-gray-500">
              <p className="mb-2">Logged in as: <span className="font-medium text-gray-700">{user?.email || 'Anonymous'}</span></p>
              <p className="break-all">User ID: <span className="font-medium text-gray-700">{user?.uid || 'N/A'}</span></p>
              <p className="mt-1">App ID: <span className="font-medium text-gray-700">{typeof __app_id !== 'undefined' ? __app_id : 'default-app-id'}</span></p>
              <Button variant="ghost" className="w-full justify-start text-lg px-3 py-2 rounded-lg hover:bg-gray-100 flex items-center gap-2 mt-4" onClick={() => signOut(auth).then(() => navigateTo('login')).catch(console.error)}>
                <LogOutIcon className="h-5 w-5" /> Logout
              </Button>
            </div>
          </aside>
          <LeadsListPage user={user} />
        </div>
      );
    case 'outreach':
        return (
            <div className="flex h-screen bg-gray-50">
                <aside className="w-64 bg-white p-6 shadow-md rounded-r-lg flex flex-col justify-between">
                    <div>
                        <div className="text-2xl font-bold text-indigo-700 mb-8">AI SDR Agent</div>
                        <nav className="space-y-4">
                            <Button variant="ghost" className="w-full justify-start text-lg px-3 py-2 rounded-lg hover:bg-gray-100 flex items-center gap-2" onClick={() => navigateTo('dashboard')}>
                            <DashboardIcon className="h-5 w-5" /> Dashboard
                            </Button>
                            <Button variant="ghost" className="w-full justify-start text-lg px-3 py-2 rounded-lg hover:bg-gray-100 flex items-center gap-2" onClick={() => navigateTo('leads')}>
                            <UsersIcon className="h-5 w-5" /> Leads
                            </Button>
                            <Button variant="ghost" className="w-full justify-start text-lg px-3 py-2 rounded-lg hover:bg-gray-100 flex items-center gap-2" onClick={() => navigateTo('outreach')}>
                            <MailIcon className="h-5 w-5" /> Outreach
                            </Button>
                            <Button variant="ghost" className="w-full justify-start text-lg px-3 py-2 rounded-lg hover:bg-gray-100 flex items-center gap-2" onClick={() => alert('Feature Coming Soon: Analytics & Reporting!')}>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5"><path d="M12 20V10"/><path d="M18 20V4"/><path d="M6 20v-4"/></svg> Analytics
                            </Button>
                        </nav>
                    </div>
                    <div className="text-sm text-gray-500">
                        <p className="mb-2">Logged in as: <span className="font-medium text-gray-700">{user?.email || 'Anonymous'}</span></p>
                        <p className="break-all">User ID: <span className="font-medium text-gray-700">{user?.uid || 'N/A'}</span></p>
                        <p className="mt-1">App ID: <span className="font-medium text-gray-700">{typeof __app_id !== 'undefined' ? __app_id : 'default-app-id'}</span></p>
                        <Button variant="ghost" className="w-full justify-start text-lg px-3 py-2 rounded-lg hover:bg-gray-100 flex items-center gap-2 mt-4" onClick={() => signOut(auth).then(() => navigateTo('login')).catch(console.error)}>
                            <LogOutIcon className="h-5 w-5" /> Logout
                        </Button>
                    </div>
                </aside>
                <OutreachPage user={user} /> {/* Pass user prop to OutreachPage */}
            </div>
        );
    default:
      return <LoginPage onLoginSuccess={() => navigateTo('dashboard')} />;
  }
};

// Root component that includes the AuthProvider
const Root = () => (
  <AuthProvider>
    <App />
  </AuthProvider>
);

export default Root;
