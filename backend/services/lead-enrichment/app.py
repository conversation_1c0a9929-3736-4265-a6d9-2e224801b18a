#!/usr/bin/env python3
"""
AI SDR Lead Enrichment Service
A Python microservice for web scraping, lead discovery, and data enrichment
Based on the Grok AI SDR Agent Guide
"""

import os
import json
import logging
import sqlite3
from datetime import datetime
from typing import List, Dict, Optional

import requests
import pandas as pd
from bs4 import BeautifulSoup
from flask import Flask, request, jsonify
from flask_cors import CORS
import schedule
import time
import threading

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Configuration
DATABASE_PATH = os.getenv('DATABASE_PATH', 'leads_enrichment.db')
HUNTER_API_KEY = os.getenv('HUNTER_API_KEY', '')  # Free tier: 25 searches/month
USER_AGENT = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'

class LeadEnrichmentService:
    def __init__(self):
        self.init_database()
    
    def init_database(self):
        """Initialize SQLite database for lead storage"""
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS enriched_leads (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                email TEXT,
                company TEXT,
                phone TEXT,
                website TEXT,
                industry TEXT,
                revenue TEXT,
                location TEXT,
                employees TEXT,
                source_url TEXT,
                confidence_score REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS scraping_sources (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                url TEXT NOT NULL,
                selector_config TEXT,
                last_scraped TIMESTAMP,
                status TEXT DEFAULT 'active'
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info("Database initialized successfully")
    
    def scrape_mining_directory(self, url: str, selectors: Dict = None) -> List[Dict]:
        """
        Scrape leads from mining industry directories
        Based on the guide's approach with configurable selectors
        """
        if not selectors:
            selectors = {
                'company_listing': '.company-listing',
                'company_name': '.company-name',
                'contact_email': '.contact-email',
                'revenue': '.revenue',
                'location': '.location',
                'phone': '.phone',
                'website': '.website'
            }
        
        try:
            headers = {'User-Agent': USER_AGENT}
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            leads = []
            
            for item in soup.select(selectors['company_listing']):
                lead = {
                    'name': self._extract_text(item, selectors['company_name']),
                    'email': self._extract_text(item, selectors['contact_email']),
                    'revenue': self._extract_text(item, selectors['revenue']),
                    'location': self._extract_text(item, selectors['location']),
                    'phone': self._extract_text(item, selectors['phone']),
                    'website': self._extract_text(item, selectors['website']),
                    'source_url': url,
                    'industry': 'mining',
                    'confidence_score': 0.7  # Base confidence for scraped data
                }
                
                # Only add leads with at least name and one contact method
                if lead['name'] and (lead['email'] or lead['phone']):
                    leads.append(lead)
            
            logger.info(f"Scraped {len(leads)} leads from {url}")
            return leads
            
        except Exception as e:
            logger.error(f"Error scraping {url}: {str(e)}")
            return []
    
    def _extract_text(self, item, selector: str) -> str:
        """Extract text from HTML element with error handling"""
        try:
            element = item.select_one(selector)
            return element.text.strip() if element else ''
        except:
            return ''
    
    def enrich_with_hunter(self, company_name: str, domain: str = None) -> Dict:
        """
        Enrich lead data using Hunter.io API (25 free searches/month)
        """
        if not HUNTER_API_KEY:
            return {}
        
        try:
            if domain:
                url = f"https://api.hunter.io/v2/domain-search?domain={domain}&api_key={HUNTER_API_KEY}"
            else:
                url = f"https://api.hunter.io/v2/domain-search?company={company_name}&api_key={HUNTER_API_KEY}"
            
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            if data.get('data'):
                domain_info = data['data']
                return {
                    'domain': domain_info.get('domain'),
                    'organization': domain_info.get('organization'),
                    'country': domain_info.get('country'),
                    'emails': [email['value'] for email in domain_info.get('emails', [])[:3]],  # Top 3 emails
                    'confidence_score': 0.9  # High confidence for Hunter data
                }
        except Exception as e:
            logger.error(f"Hunter enrichment error for {company_name}: {str(e)}")
        
        return {}
    
    def filter_leads_by_criteria(self, leads: List[Dict], criteria: Dict = None) -> List[Dict]:
        """
        Filter leads based on revenue, location, and other criteria
        Following the guide's targeting approach
        """
        if not criteria:
            criteria = {
                'revenue_range': ['10M-50M', '20M-50M', '$10M', '$20M', '$30M', '$40M', '$50M'],
                'target_locations': ['Africa', 'Canada', 'DRC', 'South Africa', 'Ghana', 'Zambia'],
                'min_confidence': 0.5
            }
        
        df = pd.DataFrame(leads)
        if df.empty:
            return []
        
        # Filter by revenue
        if 'revenue_range' in criteria:
            revenue_pattern = '|'.join(criteria['revenue_range'])
            df = df[df['revenue'].str.contains(revenue_pattern, case=False, na=False)]
        
        # Filter by location
        if 'target_locations' in criteria:
            location_pattern = '|'.join(criteria['target_locations'])
            df = df[df['location'].str.contains(location_pattern, case=False, na=False)]
        
        # Filter by confidence score
        if 'min_confidence' in criteria:
            df = df[df['confidence_score'] >= criteria['min_confidence']]
        
        # Calculate lead score based on criteria match
        df['lead_score'] = 0
        df.loc[df['revenue'].str.contains('20M-50M', case=False, na=False), 'lead_score'] += 10
        df.loc[df['location'].str.contains('Africa|Canada', case=False, na=False), 'lead_score'] += 5
        df.loc[df['email'].notna() & (df['email'] != ''), 'lead_score'] += 3
        df.loc[df['phone'].notna() & (df['phone'] != ''), 'lead_score'] += 2
        
        # Sort by lead score
        df = df.sort_values('lead_score', ascending=False)
        
        logger.info(f"Filtered to {len(df)} high-quality leads")
        return df.to_dict('records')
    
    def save_enriched_leads(self, leads: List[Dict]) -> int:
        """Save enriched leads to database"""
        if not leads:
            return 0
        
        conn = sqlite3.connect(DATABASE_PATH)
        df = pd.DataFrame(leads)
        
        # Add timestamp
        df['updated_at'] = datetime.now().isoformat()
        
        # Save to database
        rows_added = df.to_sql('enriched_leads', conn, if_exists='append', index=False)
        conn.close()
        
        logger.info(f"Saved {rows_added} enriched leads to database")
        return rows_added
    
    def get_enriched_leads(self, limit: int = 100, min_score: int = 5) -> List[Dict]:
        """Retrieve enriched leads from database"""
        conn = sqlite3.connect(DATABASE_PATH)
        
        query = '''
            SELECT * FROM enriched_leads 
            WHERE lead_score >= ? 
            ORDER BY lead_score DESC, created_at DESC 
            LIMIT ?
        '''
        
        df = pd.read_sql_query(query, conn, params=[min_score, limit])
        conn.close()
        
        return df.to_dict('records')

# Initialize service
enrichment_service = LeadEnrichmentService()

# API Routes
@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'Lead Enrichment Service',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/scrape', methods=['POST'])
def scrape_leads():
    """Scrape leads from a given URL"""
    data = request.get_json()
    url = data.get('url')
    selectors = data.get('selectors', {})
    
    if not url:
        return jsonify({'error': 'URL is required'}), 400
    
    try:
        leads = enrichment_service.scrape_mining_directory(url, selectors)
        return jsonify({
            'success': True,
            'leads_found': len(leads),
            'leads': leads[:10],  # Return first 10 for preview
            'message': f'Successfully scraped {len(leads)} leads'
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/enrich', methods=['POST'])
def enrich_leads():
    """Enrich leads with additional data"""
    data = request.get_json()
    leads = data.get('leads', [])
    criteria = data.get('criteria', {})
    
    try:
        # Filter leads by criteria
        filtered_leads = enrichment_service.filter_leads_by_criteria(leads, criteria)
        
        # Enrich with Hunter.io (limited by API quota)
        enriched_leads = []
        for lead in filtered_leads[:5]:  # Limit to 5 to preserve API quota
            hunter_data = enrichment_service.enrich_with_hunter(
                lead.get('name', ''), 
                lead.get('website', '')
            )
            if hunter_data:
                lead.update(hunter_data)
            enriched_leads.append(lead)
        
        # Save to database
        saved_count = enrichment_service.save_enriched_leads(enriched_leads)
        
        return jsonify({
            'success': True,
            'leads_processed': len(filtered_leads),
            'leads_enriched': len(enriched_leads),
            'leads_saved': saved_count,
            'enriched_leads': enriched_leads
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/leads', methods=['GET'])
def get_leads():
    """Get enriched leads from database"""
    limit = request.args.get('limit', 100, type=int)
    min_score = request.args.get('min_score', 5, type=int)
    
    try:
        leads = enrichment_service.get_enriched_leads(limit, min_score)
        return jsonify({
            'success': True,
            'count': len(leads),
            'leads': leads
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/sources', methods=['GET', 'POST'])
def manage_sources():
    """Manage scraping sources"""
    if request.method == 'GET':
        conn = sqlite3.connect(DATABASE_PATH)
        df = pd.read_sql_query('SELECT * FROM scraping_sources', conn)
        conn.close()
        return jsonify(df.to_dict('records'))
    
    elif request.method == 'POST':
        data = request.get_json()
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO scraping_sources (name, url, selector_config)
            VALUES (?, ?, ?)
        ''', (data['name'], data['url'], json.dumps(data.get('selectors', {}))))
        
        conn.commit()
        conn.close()
        
        return jsonify({'success': True, 'message': 'Source added successfully'})

if __name__ == '__main__':
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('DEBUG', 'False').lower() == 'true'
    
    logger.info(f"Starting Lead Enrichment Service on port {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)
