/**
 * Minimal deployment script for AI SDR backend
 * This creates a simple Express server that can be deployed to various platforms
 */

const express = require('express');
const cors = require('cors');
const { AIService } = require('./services/shared/utils/aiService');

const app = express();
const port = process.env.PORT || 3001;

// Add logging for debugging
console.log('🚀 Starting AI SDR Backend...');
console.log('📍 Port:', port);
console.log('🌍 Environment:', process.env.NODE_ENV || 'development');
console.log('🔑 Gemini API Key:', process.env.GEMINI_API_KEY ? 'Set' : 'Missing');
console.log('📧 SendGrid API Key:', process.env.SENDGRID_API_KEY ? 'Set' : 'Missing');
console.log('🔐 JWT Secret:', process.env.JWT_SECRET ? 'Set' : 'Missing');

// Middleware
app.use(cors());
app.use(express.json());

// Initialize AI Service with error handling
let aiService;
try {
  aiService = new AIService();
  console.log('✅ AI Service initialized successfully');
} catch (error) {
  console.error('❌ AI Service initialization failed:', error.message);
  aiService = null;
}

// Health check endpoint
app.get('/health', (req, res) => {
  console.log('🏥 Health check requested');
  try {
    const healthStatus = {
      status: 'healthy',
      service: 'AI SDR Backend',
      timestamp: new Date().toISOString(),
      geminiAvailable: aiService ? aiService.isAvailable() : false,
      environment: process.env.NODE_ENV || 'development',
      port: port,
      uptime: process.uptime()
    };
    console.log('✅ Health check successful:', healthStatus);
    res.json(healthStatus);
  } catch (error) {
    console.error('❌ Health check failed:', error);
    res.status(500).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// AI-powered email generation endpoint
app.post('/api/generate-email', async (req, res) => {
  try {
    const { lead, campaign } = req.body;
    
    if (!lead || !campaign) {
      return res.status(400).json({
        error: 'Missing required fields: lead and campaign'
      });
    }

    const result = await aiService.generatePersonalizedEmail(lead, campaign);
    
    res.json({
      success: true,
      email: result,
      generatedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Email generation error:', error);
    res.status(500).json({
      error: 'Failed to generate email',
      message: error.message
    });
  }
});

// AI-powered lead scoring endpoint
app.post('/api/score-lead', async (req, res) => {
  try {
    const { lead } = req.body;
    
    if (!lead) {
      return res.status(400).json({
        error: 'Missing required field: lead'
      });
    }

    const result = await aiService.calculateLeadScore(lead);
    
    res.json({
      success: true,
      score: result,
      scoredAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Lead scoring error:', error);
    res.status(500).json({
      error: 'Failed to score lead',
      message: error.message
    });
  }
});

// Campaign strategy generation endpoint
app.post('/api/generate-strategy', async (req, res) => {
  try {
    const { leads, campaignGoals } = req.body;
    
    if (!leads || !campaignGoals) {
      return res.status(400).json({
        error: 'Missing required fields: leads and campaignGoals'
      });
    }

    const result = await aiService.generateCampaignStrategy(leads, campaignGoals);
    
    res.json({
      success: true,
      strategy: result,
      generatedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Strategy generation error:', error);
    res.status(500).json({
      error: 'Failed to generate strategy',
      message: error.message
    });
  }
});

// API documentation endpoint
app.get('/api/docs', (req, res) => {
  res.json({
    title: 'AI SDR Backend API',
    version: '1.0.0',
    description: 'AI-powered Sales Development Representative backend with Google Gemini integration',
    endpoints: {
      'GET /health': 'Health check and service status',
      'POST /api/generate-email': 'Generate personalized emails using AI',
      'POST /api/score-lead': 'Calculate AI-powered lead scores',
      'POST /api/generate-strategy': 'Generate campaign strategies'
    },
    geminiIntegration: aiService.isAvailable()
  });
});

// Start server
if (require.main === module) {
  const server = app.listen(port, '0.0.0.0', () => {
    console.log(`🚀 AI SDR Backend running on port ${port}`);
    console.log(`📚 API Documentation: http://localhost:${port}/api/docs`);
    console.log(`❤️  Health Check: http://localhost:${port}/health`);
    console.log(`🤖 Gemini AI: ${aiService ? (aiService.isAvailable() ? 'Available' : 'Not configured') : 'Failed to initialize'}`);
    console.log(`🌍 Server listening on 0.0.0.0:${port}`);
  });

  // Handle server errors
  server.on('error', (error) => {
    console.error('❌ Server error:', error);
    process.exit(1);
  });

  // Graceful shutdown
  process.on('SIGTERM', () => {
    console.log('🛑 SIGTERM received, shutting down gracefully');
    server.close(() => {
      console.log('✅ Server closed');
      process.exit(0);
    });
  });
}

module.exports = app;
