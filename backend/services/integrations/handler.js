const Joi = require('joi');
const {
  lambdaWrapper,
  parseBody,
  getPathParameters,
  successResponse,
  errorResponse,
  validateInput,
  ValidationError,
  logger
} = require('../shared/utils/api');
const { crmService } = require('../shared/utils/crm');

// Validation schemas
const connectCrmSchema = Joi.object({
  crmType: Joi.string().valid('hubspot', 'zoho', 'salesforce').required(),
  credentials: Joi.object().required()
});

const syncLeadsSchema = Joi.object({
  crmType: Joi.string().valid('hubspot', 'zoho', 'salesforce').required(),
  direction: Joi.string().valid('import', 'export', 'bidirectional').required(),
  leads: Joi.array().items(Joi.object()).optional(),
  filters: Joi.object().optional(),
  mapping: Joi.object().optional()
});

const crmHandler = async (event) => {
  const body = parseBody(event);
  const validatedData = validateInput(body, connectCrmSchema);

  logger.info('Connecting to CRM', { crmType: validatedData.crmType });

  try {
    const connection = await crmService.connectCRM(validatedData);
    return successResponse(connection);
  } catch (error) {
    logger.error('CRM connection failed', {
      crmType: validatedData.crmType,
      error: error.message
    });
    throw error;
  }
};

const syncHandler = async (event) => {
  const body = parseBody(event);
  const validatedData = validateInput(body, syncLeadsSchema);

  logger.info('Starting CRM sync', {
    crmType: validatedData.crmType,
    direction: validatedData.direction
  });

  try {
    const result = await crmService.syncLeads(validatedData);
    return successResponse(result);
  } catch (error) {
    logger.error('CRM sync failed', {
      crmType: validatedData.crmType,
      direction: validatedData.direction,
      error: error.message
    });
    throw error;
  }
};

const getConnectionStatus = async (event) => {
  const { crmType } = getPathParameters(event);

  if (!crmType) {
    throw new ValidationError('CRM type is required');
  }

  logger.info('Getting CRM connection status', { crmType });

  try {
    const status = crmService.getConnectionStatus(crmType);
    return successResponse(status);
  } catch (error) {
    logger.error('Failed to get connection status', { crmType, error: error.message });
    throw error;
  }
};

const disconnectCRM = async (event) => {
  const { crmType } = getPathParameters(event);

  if (!crmType) {
    throw new ValidationError('CRM type is required');
  }

  logger.info('Disconnecting from CRM', { crmType });

  try {
    const result = crmService.disconnect(crmType);
    return successResponse(result);
  } catch (error) {
    logger.error('Failed to disconnect from CRM', { crmType, error: error.message });
    throw error;
  }
};

const testConnection = async (event) => {
  const { crmType } = getPathParameters(event);

  if (!crmType) {
    throw new ValidationError('CRM type is required');
  }

  logger.info('Testing CRM connection', { crmType });

  try {
    const status = crmService.getConnectionStatus(crmType);

    if (!status.connected) {
      throw new ValidationError(`No active connection to ${crmType}`);
    }

    // For now, just return the connection status
    // In a real implementation, this would test the actual connection
    return successResponse({
      crmType,
      connected: true,
      tested: true,
      testResult: 'Connection is active'
    });
  } catch (error) {
    logger.error('Connection test failed', { crmType, error: error.message });
    throw error;
  }
};

// Main handler for API Gateway routing
const main = lambdaWrapper(async (event, context) => {
  const { httpMethod, path, pathParameters } = event;
  const pathSegments = path.split('/').filter(Boolean);
  const action = pathSegments[pathSegments.length - 1];
  const crmType = pathParameters?.crmType;

  logger.info('Integrations service request', { httpMethod, path, action, crmType });

  switch (httpMethod) {
    case 'POST':
      switch (action) {
        case 'connect':
          return await crmHandler(event, context);
        case 'sync':
          return await syncHandler(event, context);
        default:
          throw new ValidationError(`Unknown action: ${action}`);
      }
    case 'GET':
      switch (action) {
        case 'status':
          return await getConnectionStatus(event, context);
        case 'test':
          return await testConnection(event, context);
        default:
          throw new ValidationError(`Unknown action: ${action}`);
      }
    case 'DELETE':
      switch (action) {
        case 'disconnect':
          return await disconnectCRM(event, context);
        default:
          throw new ValidationError(`Unknown action: ${action}`);
      }
    case 'OPTIONS':
      return successResponse({ message: 'CORS preflight' });
    default:
      throw new ValidationError(`Unsupported HTTP method: ${httpMethod}`);
  }
});

// Export wrapped handlers
module.exports = {
  main,
  crmHandler: lambdaWrapper(crmHandler),
  syncHandler: lambdaWrapper(syncHandler),
  getConnectionStatus: lambdaWrapper(getConnectionStatus),
  disconnectCRM: lambdaWrapper(disconnectCRM),
  testConnection: lambdaWrapper(testConnection)
};