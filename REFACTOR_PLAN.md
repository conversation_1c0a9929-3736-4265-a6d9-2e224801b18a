# AI SDR Backend Refactoring Plan

## 1. Current State Assessment
- **Services Implemented**: Only auth and leads services exist (50% complete)
- **Database**: In-memory arrays instead of DynamoDB
- **External Integrations**: None implemented
- **Testing**: No test coverage
- **Security**: Basic JWT implementation without proper secret management
- **Monitoring**: No logging or monitoring

## 2. Missing Microservices (Per Architecture)
- [ ] Outreach Service (email/SMS/LinkedIn)
- [ ] Analytics Service
- [ ] Integrations Service (CRM/AI/data enrichment)
- [ ] Scheduling Service

## 3. Directory Structure Gaps
```diff
backend/
  services/
    auth/
    leads/
+   outreach/
+   analytics/
+   integrations/
    shared/
+     utils/
+       api.js
+       helpers.js
+     models/
+       lead.js
+       campaign.js
+   tests/
+     unit/
+     integration/
```

## 4. Technology Stack Adjustments Needed
- **Add Dependencies**:
  - `@sendgrid/mail` for email
  - `twilio` for SMS
  - `relevanceai` SDK
  - `google-gemini` API client
  - `winston` for logging

- **Infrastructure**:
  - Implement SQS queues
  - Configure CloudWatch logging
  - Set up AWS Secrets Manager

## 5. Key Refactoring Tasks

### Authentication Service
- [ ] Migrate to DynamoDB users table
- [ ] Implement proper secret management
- [ ] Add rate limiting
- [ ] Enhance error handling

### Leads Service
- [ ] Implement DynamoDB integration
- [ ] Add lead enrichment via Apollo.io/Clearbit
- [ ] Integrate AI scoring (Relevance AI)
- [ ] Add proper filtering/sorting

### New Services
- [ ] Outreach Service (SendGrid/Twilio/LinkedIn)
- [ ] Analytics Service (CloudWatch/X-Ray)
- [ ] Integrations Service (HubSpot/Zoho)

## 6. Implementation Phases

1. **Phase 1**: Core Services Refactor (2 weeks)
   - Auth/Leads service improvements
   - Shared utils/models implementation
   - Basic testing infrastructure

2. **Phase 2**: New Services (3 weeks)
   - Outreach service
   - Analytics service
   - Initial monitoring

3. **Phase 3**: Integrations (2 weeks)
   - CRM integrations
   - AI services
   - Data enrichment

## 7. Risk Mitigation
- Implement feature flags for gradual rollout
- Maintain detailed migration documentation
- Set up comprehensive monitoring before launch