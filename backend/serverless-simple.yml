service: ai-sdr-backend-simple

frameworkVersion: '3'

provider:
  name: aws
  runtime: nodejs18.x
  region: us-east-1
  stage: staging
  environment:
    STAGE: staging
    REGION: us-east-1
    GEMINI_API_KEY: ${env:STAGING_GEMINI_API_KEY}
    SENDGRID_API_KEY: ${env:STAGING_SENDGRID_API_KEY}
    JWT_SECRET: ${env:STAGING_JWT_SECRET}

functions:
  api:
    handler: services/shared/utils/api.handler
    events:
      - http:
          path: /{proxy+}
          method: ANY
          cors: true
      - http:
          path: /
          method: ANY
          cors: true

  # Health check function
  health:
    handler: services/shared/utils/api.health
    events:
      - http:
          path: /health
          method: GET
          cors: true

  # AI-powered lead scoring
  leadScore:
    handler: services/leads/handler.calculateLeadScore
    events:
      - http:
          path: /api/leads/{id}/score
          method: POST
          cors: true

  # Email generation
  generateEmail:
    handler: services/outreach/handler.generateEmail
    events:
      - http:
          path: /api/outreach/generate-email
          method: POST
          cors: true

  # Lead enrichment
  enrichLead:
    handler: services/leads/handler.enrichLead
    events:
      - http:
          path: /api/leads/enrich
          method: POST
          cors: true
