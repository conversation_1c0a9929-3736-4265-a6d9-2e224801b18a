# Product Requirements Document: AI SDR Agent for Mining Equipment Suppliers

## 1. Product Overview
The AI Sales Development Representative (SDR) Agent is an automated tool designed to assist mining equipment suppliers in identifying and engaging potential customers. It leverages AI to collect data on mining companies, score leads based on their potential, generate personalized outreach messages, and send emails, utilizing cost-effective tools like [Relevance AI](https://relevanceai.com/agents) and [SendGrid](https://sendgrid.com/) to minimize costs.

## 2. User Stories
The following user stories capture the core functionality of the MVP, written in Gherkin format to reflect the needs of sales representatives and administrators in mining equipment supplier companies.

1. **As a user, I want to log into the system so that I can access the dashboard.**
   - Given that I am on the login page
   - When I enter my username and password
   - And click the login button
   - Then I should be redirected to the dashboard if credentials are correct
   - Else I should see an error message

2. **As a user, I want to view a list of leads so that I can see potential customers.**
   - Given that I am logged in
   - When I navigate to the leads page
   - Then I should see a table with leads, including columns for company name, score, status, and last contacted date

3. **As a user, I want to filter leads by score so that I can focus on high-potential leads.**
   - Given that I am on the leads page
   - When I select a score range in the filter
   - Then the lead list should update to show only leads within that score range

4. **As a user, I want to sort leads by score in descending order so that I can see the best leads first.**
   - Given that I am on the leads page
   - When I click the score column header
   - Then the leads should be sorted by score descending

5. **As a user, I want to view detailed information about a lead so that I can understand their potential.**
   - Given that I am on the leads page
   - When I click on a lead's name
   - Then I should be taken to the lead details page showing company information, scoring details, outreach message, and email status

6. **As a user, I want to see the outreach message generated for a lead so that I can review it.**
   - Given that I am on the lead details page
   - Then I should see the generated outreach message

7. **As a user, I want to edit the outreach message if needed before sending.**
   - Given that I am on the lead details page
   - When I click the edit button next to the outreach message
   - Then I can modify the message text
   - And save the changes

8. **As a user, I want to send the outreach email to a lead.**
   - Given that I am on the lead details page
   - And the lead has not been contacted yet
   - When I click the send email button
   - Then the system should send the email and update the status

9. **As a user, I want to see the status of sent emails.**
   - Given that I am on the lead details page
   - Then I should see the email status (e.g., sent, delivered, opened)

10. **As a user, I want to resend an email if it failed to send.**
    - Given that an email failed to send
    - When I click the resend button
    - Then the system should attempt to send the email again

11. **As an administrator, I want to trigger the lead processing job manually.**
    - Given that I am logged in as admin
    - When I navigate to the job management page
    - And click the run lead processing button
    - Then the system should start the lead processing job

12. **As an administrator, I want to monitor the status of running jobs.**
    - Given that a job is running
    - When I view the job management page
    - Then I should see the job's progress and any logs

13. **As a user, I want to receive notifications when a job completes or fails.**
    - Given that a job has completed or failed
    - Then I should receive an email notification with the result

14. **As a user, I want to export the lead list to CSV.**
    - Given that I am on the leads page
    - When I click the export button
    - Then I should download a CSV file with all lead data

15. **As an administrator, I want to configure the email service API key.**
    - Given that I am on the settings page
    - When I enter the SendGrid API key
    - And save the settings
    - Then the system should use that key for sending emails

16. **As a user, I want to see key metrics on the dashboard.**
    - Given that I am on the dashboard
    - Then I should see metrics like total leads, emails sent, response rate, etc.

17. **As a user, I want to archive leads that are no longer relevant.**
    - Given that I am on the lead details page
    - When I click the archive button
    - Then the lead should be marked as archived and removed from the active list

18. **As a user, I want to search for leads by company name.**
    - Given that I am on the leads page
    - When I enter a search term in the search bar
    - Then the lead list should show only leads matching the search term

19. **As an administrator, I want to set the schedule for automated lead processing.**
    - Given that I am on the settings page
    - When I configure the schedule (e.g., daily at 2 AM)
    - Then the system should run the lead processing job automatically at that time

20. **As a user, I want to view the history of emails sent to a lead.**
    - Given that I am on the lead details page
    - Then I should see a list of all emails sent to that lead, with timestamps and statuses

21. **As a user, I want to filter leads by region so that I can target specific markets.**
    - Given that I am on the leads page
    - When I select a region in the filter
    - Then the lead list should update to show only leads from that region

22. **As a user, I want to see a summary of recent job activities on the dashboard.**
    - Given that I am on the dashboard
    - Then I should see a list of recent job activities with their statuses

## 3. User Flows
The following user flows outline the primary interactions with the AI SDR Agent for the MVP.

### Lead Processing Flow (Automated)
- **Trigger**: Scheduled or manually initiated by an administrator.
- **Steps**:
  1. System executes a data collection script to scrape mining company data from predefined public sources (e.g., mining directories, industry reports).
  2. For each new company identified, a lead record is created in the database with attributes like company name, contact email, revenue, and expansion plans.
  3. For each lead, the system calls the [Relevance AI API](https://relevanceai.com/agents) to score the lead based on predefined criteria (e.g., revenue > $10M, recent expansion announcements).
  4. If the lead score exceeds a threshold (e.g., 70/100), the system calls the [Relevance AI API](https://relevanceai.com/agents) to generate a personalized outreach message.
  5. The system sends the outreach email via [SendGrid API](https://sendgrid.com/).
  6. The lead status is updated to "contacted" in the database.

### User Monitoring Flow
- **Steps**:
  1. User logs into the system via the login screen.
  2. User views the dashboard displaying key metrics (e.g., total leads, emails sent).
  3. User navigates to the leads page to view a list of leads.
  4. User applies filters (e.g., score range, region) or sorts the list (e.g., by score).
  5. User clicks on a lead to access the lead details page.
  6. User reviews the outreach message and email status.
  7. If necessary, user edits the outreach message and resends the email.

### Configuration Flow
- **Steps**:
  1. Administrator navigates to the settings page.
  2. Administrator inputs the [SendGrid API](https://sendgrid.com/) key for email integration.
  3. Administrator sets the schedule for automated lead processing jobs (e.g., daily at 2 AM).
  4. Administrator saves the settings, which are stored in the database.

## 4. Screens and UI/UX
The MVP includes a minimal set of screens designed for simplicity and functionality, targeting sales representatives and administrators in mining equipment supplier companies. The UI uses a clean, responsive design with [Tailwind CSS](https://tailwindcss.com/) for styling.

1. **Login Screen**
   - **Description**: Allows users to authenticate.
   - **UI Elements**:
     - Username and password input fields.
     - Login button.
     - Optional "Forgot Password" link.
   - **Interactions**: Submitting credentials redirects to the dashboard or displays an error.

2. **Dashboard**
   - **Description**: Provides an overview of system activity.
   - **UI Elements**:
     - Widgets displaying total leads, leads contacted, emails sent, and response rate.
     - Recent activity log showing job statuses.
   - **Interactions**: Links to leads and job management pages.

3. **Leads List**
   - **Description**: Displays a table of leads for review.
   - **UI Elements**:
     - Table with columns: Company Name, Score, Status, Last Contacted.
     - Filters for score range, status, and region.
     - Sortable column headers.
     - Search bar for company name.
     - Export to CSV button.
   - **Interactions**: Clicking a lead navigates to the lead details page.

4. **Lead Details**
   - **Description**: Shows detailed information about a specific lead.
   - **UI Elements**:
     - Company Information section: Name, Contact Email, Revenue, Expansion Plans.
     - Lead Score section: Numerical score and breakdown.
     - Outreach Message section: Text area with edit button.
     - Email History section: List of sent emails with timestamps and statuses.
     - Action buttons: Send Email, Archive Lead.
   - **Interactions**: Edit message, send/resend email, archive lead.

5. **Job Management**
   - **Description**: Allows administrators to trigger and monitor lead processing jobs.
   - **UI Elements**:
     - List of recent jobs with status, start time, and end time.
     - Button to trigger new lead processing job.
     - Job details view with logs.
   - **Interactions**: Trigger job, view job details.

6. **Settings**
   - **Description**: Enables configuration of system parameters.
   - **UI Elements**:
     - Form fields for SendGrid API key, job schedule.
     - Save button.
   - **Interactions**: Update and save settings.

## 5. Features and Functionality
The MVP focuses on core functionalities to deliver value to mining equipment suppliers while maintaining cost efficiency.

- **Lead Data Collection**: Automated web scraping from public mining industry sources using Python libraries like [BeautifulSoup](https://www.crummy.com/software/BeautifulSoup/) or [Scrapy](https://scrapy.org/).
- **Lead Scoring**: AI-based scoring via [Relevance AI API](https://relevanceai.com/agents), evaluating leads based on revenue, expansion plans, and other criteria.
- **Outreach Message Generation**: AI-generated personalized emails using [Relevance AI API](https://relevanceai.com/agents), tailored to lead-specific data (e.g., referencing cobalt production for DRC-based companies).
- **Email Sending**: Integration with [SendGrid API](https://sendgrid.com/) for sending up to 100 emails/day in the free tier.
- **User Dashboard**: Web interface built with [React](https://reactjs.org/) and [Tailwind CSS](https://tailwindcss.com/) for monitoring leads and jobs.
- **Authentication**: Secure user login using JSON Web Tokens (JWT).
- **Job Scheduling**: Manual or scheduled lead processing jobs, configurable via the settings page.

## 6. Technical Architecture
The system adopts a serverless, modular architecture to ensure scalability and cost efficiency, leveraging free or low-cost tools.

| **Component**       | **Technology**                     | **Description**                                                                 |
|---------------------|------------------------------------|---------------------------------------------------------------------------------|
| Frontend            | React.js, Tailwind CSS             | Hosted on [Netlify](https://www.netlify.com/) for static site hosting.          |
| Backend             | AWS Lambda, API Gateway            | Serverless functions for API handling, hosted on [AWS](https://aws.amazon.com/).|
| Database            | AWS DynamoDB                       | NoSQL database for storing lead and job data, using free tier.                  |
| AI Services         | Relevance AI API                   | Handles lead scoring and message generation.                                    |
| Email Service       | SendGrid API                       | Sends outreach emails, limited to 100/day in free tier.                         |
| Data Collection     | Python, BeautifulSoup/Scrapy       | Serverless functions for web scraping, run on AWS Lambda.                       |

**Interactions**:
- Frontend communicates with backend via REST API.
- Backend orchestrates data collection, AI processing, and email sending.
- Data collection scripts run as scheduled or triggered Lambda functions.

## 7. System Design
- **Frontend**: Single-page React application with components for login, dashboard, leads list, lead details, job management, and settings. Uses [Axios](https://axios-http.com/) for API calls and [Tailwind CSS](https://tailwindcss.com/) for styling.
- **Backend**: AWS Lambda functions handle:
  - User authentication using JWT.
  - CRUD operations for leads and jobs.
  - Integration with [Relevance AI API](https://relevanceai.com/agents) for scoring and message generation.
  - Email sending via [SendGrid API](https://sendgrid.com/).
- **Database**: AWS DynamoDB with tables for users, leads, and jobs.
- **Data Collection**: Lambda function running Python scripts with [BeautifulSoup](https://www.crummy.com/software/BeautifulSoup/) to scrape public mining directories, storing data in DynamoDB.
- **AI Integration**: Backend functions send lead data to [Relevance AI API](https://relevanceai.com/agents) and store results in DynamoDB.
- **Email Sending**: Backend functions call [SendGrid API](https://sendgrid.com/) to send emails, updating lead status in DynamoDB.

## 8. API Specifications
The backend exposes REST API endpoints for frontend and job interactions.

| **Endpoint**                     | **Method** | **Purpose**                                      | **Request**                              | **Response**                             |
|----------------------------------|------------|--------------------------------------------------|------------------------------------------|------------------------------------------|
| `/auth/login`                    | POST       | Authenticate user and return JWT token.          | `{ "username": string, "password": string }` | `{ "token": string }`                    |
| `/leads`                         | GET        | Retrieve list of leads with filters and sorting.  | Query params: `?filter={}&sort={}`       | `[ { "id": string, "company_name": string, "score": number, "status": string, ... } ]` |
| `/leads/{id}`                    | GET        | Get details of a specific lead.                   | None                                     | `{ "id": string, "company_name": string, "contact_email": string, "revenue": number, "expansion_plans": string, "score": number, "outreach_message": string, "email_status": string, ... }` |
| `/leads/{id}/send-email`         | POST       | Send or resend outreach email for a lead.         | `{ "message": string }` (optional)        | `{ "success": boolean, "message": string }` |
| `/jobs/run-lead-processing`       | POST       | Trigger lead processing job.                     | None                                     | `{ "job_id": string }`                   |
| `/jobs/{id}`                     | GET        | Get status and logs of a job.                    | None                                     | `{ "id": string, "status": string, "logs": [string], ... }` |

## 9. Data Model
The system uses a NoSQL database (AWS DynamoDB) with the following entities:

| **Entity** | **Attributes**                                                                 |
|------------|-------------------------------------------------------------------------------|
| **User**   | `id`: string, `username`: string, `password_hash`: string, `role`: string (admin, user) |
| **Lead**   | `id`: string, `company_name`: string, `contact_email`: string, `revenue`: number, `expansion_plans`: string, `score`: number, `outreach_message`: string, `email_status`: string (pending, sent, delivered, opened), `created_at`: timestamp, `updated_at`: timestamp |
| **Job**    | `id`: string, `type`: string (lead_processing), `status`: string (running, completed, failed), `started_at`: timestamp, `completed_at`: timestamp, `logs`: array of strings |

**Relationships**:
- Users can access many leads (based on role).
- Jobs are standalone, linked to system processes.

## 10. Security Considerations
- **HTTPS**: All communications use HTTPS to encrypt data in transit.
- **Authentication**: JWT-based authentication for secure user access.
- **Password Storage**: Passwords hashed using bcrypt.
- **Input Validation**: Sanitize all user inputs to prevent injection attacks.
- **Access Control**: Role-based access (admin vs. user) for sensitive operations.
- **API Key Security**: Store [SendGrid](https://sendgrid.com/) and [Relevance AI](https://relevanceai.com/agents) API keys in AWS Secrets Manager or environment variables.

## 11. Performance Requirements
- **API Response Time**: < 200ms for 95% of requests under normal load.
- **Lead Processing Time**: < 5 minutes for processing 100 leads, including data collection, scoring, and email sending.
- **Database Query Time**: < 100ms for typical queries (e.g., retrieving lead list).
- **Email Sending**: Process up to 100 emails/day within [SendGrid](https://sendgrid.com/) free tier limits.

## 12. Scalability Considerations
- **Serverless Architecture**: [AWS Lambda](https://aws.amazon.com/lambda/) scales automatically with load.
- **Database Scalability**: [AWS DynamoDB](https://aws.amazon.com/dynamodb/) supports horizontal scaling via partitioning.
- **Future Enhancements**: Add message queues (e.g., [AWS SQS](https://aws.amazon.com/sqs/)) for asynchronous processing of large lead volumes.

## 13. Testing Strategy
- **Unit Tests**: Test backend Lambda functions using [Jest](https://jestjs.io/) for business logic.
- **Integration Tests**: Verify API endpoints and integrations with [Relevance AI](https://relevanceai.com/agents) and [SendGrid](https://sendgrid.com/).
- **End-to-End Tests**: Use [Cypress](https://www.cypress.io/) to test critical user flows (e.g., login, lead viewing, email sending).
- **Manual Testing**: Validate UI/UX for usability on desktop and tablet devices.
- **Mocking**: Mock external APIs during testing to avoid rate limits and costs.

## 14. Deployment Plan
- **Version Control**: Use [GitHub](https://github.com/) for source code management.
- **CI/CD**: Set up [GitHub Actions](https://github.com/features/actions) for automated builds and deployments.
- **Frontend Deployment**: Deploy React app to [Netlify](https://www.netlify.com/) on push to main branch.
- **Backend Deployment**: Deploy Lambda functions and API Gateway using [Serverless Framework](https://www.serverless.com/) on push to main branch.
- **Database Setup**: Configure [AWS DynamoDB](https://aws.amazon.com/dynamodb/) tables via AWS console or infrastructure as code (e.g., AWS CloudFormation).
- **Environment Variables**: Store API keys securely in AWS Secrets Manager.

## 15. Maintenance and Support
- **Monitoring**: Use [AWS CloudWatch](https://aws.amazon.com/cloudwatch/) to monitor logs and performance metrics.
- **Alerts**: Set up alerts for errors or performance degradation.
- **Documentation**: Provide user and admin guides for dashboard usage and system configuration.
- **Updates**: Schedule regular updates for security patches and dependency upgrades.
- **Support**: Offer email-based support for users, with plans for a ticketing system in future iterations.