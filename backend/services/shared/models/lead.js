const Joi = require('joi');
const { DatabaseService } = require('../utils/database');
const { generateId, logger } = require('../utils/helpers');
const { ValidationError } = require('../utils/api');

// Lead validation schema
const leadSchema = Joi.object({
  leadId: Joi.string().optional(),
  companyName: Joi.string().required().min(1).max(255),
  contactEmail: Joi.string().email().required(),
  contactName: Joi.string().optional().max(255),
  phone: Joi.string().optional().max(50),
  revenue: Joi.number().positive().optional(),
  industry: Joi.string().optional().max(100),
  location: Joi.string().optional().max(255),
  expansionPlans: Joi.string().optional().max(1000),
  score: Joi.number().min(0).max(100).default(0),
  outreachMessage: Joi.string().optional().max(2000).default(''),
  emailStatus: Joi.string().valid('pending', 'sent', 'opened', 'replied', 'bounced').default('pending'),
  tags: Joi.array().items(Joi.string()).optional().default([]),
  notes: Joi.string().optional().max(2000).default(''),
  source: Joi.string().optional().max(100),
  assignedTo: Joi.string().optional()
});

const updateLeadSchema = leadSchema.fork(['companyName', 'contactEmail'], (schema) => schema.optional());

class LeadService extends DatabaseService {
  constructor() {
    super(process.env.LEADS_TABLE || 'ai-sdr-dev-leads');
  }

  getPrimaryKey() {
    return 'leadId';
  }

  /**
   * Create a new lead with validation
   * @param {Object} leadData - Lead data
   * @returns {Promise<Object>} Created lead
   */
  async createLead(leadData) {
    // Validate input
    const validatedData = leadSchema.validate(leadData);
    if (validatedData.error) {
      throw new ValidationError('Invalid lead data', validatedData.error.details);
    }

    const lead = validatedData.value;

    // Generate ID if not provided
    if (!lead.leadId) {
      lead.leadId = generateId();
    }

    logger.info('Creating new lead', { leadId: lead.leadId, companyName: lead.companyName });

    return await this.create(lead);
  }

  /**
   * Get lead by ID
   * @param {string} leadId - Lead ID
   * @returns {Promise<Object>} Lead data
   */
  async getLead(leadId) {
    return await this.get(leadId);
  }

  /**
   * Update lead with validation
   * @param {string} leadId - Lead ID
   * @param {Object} updates - Fields to update
   * @returns {Promise<Object>} Updated lead
   */
  async updateLead(leadId, updates) {
    // Validate updates
    const validatedUpdates = updateLeadSchema.validate(updates);
    if (validatedUpdates.error) {
      throw new ValidationError('Invalid update data', validatedUpdates.error.details);
    }

    logger.info('Updating lead', { leadId, updates: Object.keys(validatedUpdates.value) });

    return await this.update(leadId, validatedUpdates.value);
  }

  /**
   * Delete lead
   * @param {string} leadId - Lead ID
   * @returns {Promise<void>}
   */
  async deleteLead(leadId) {
    logger.info('Deleting lead', { leadId });
    return await this.delete(leadId);
  }

  /**
   * Get all leads with optional filtering and pagination
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Leads with pagination metadata
   */
  async getLeads(options = {}) {
    const {
      filter = {},
      sort = 'createdAt',
      order = 'desc',
      page = 1,
      limit = 20
    } = options;

    logger.info('Fetching leads', { filter, sort, order, page, limit });

    // For now, use scan - in production, consider using GSI for better performance
    const scanOptions = {};

    // Add filter expressions if provided
    if (Object.keys(filter).length > 0) {
      const filterExpressions = [];
      const expressionAttributeNames = {};
      const expressionAttributeValues = {};

      Object.entries(filter).forEach(([key, value], index) => {
        const attrName = `#attr${index}`;
        const attrValue = `:val${index}`;

        if (typeof value === 'string' && value.includes('*')) {
          // Wildcard search
          filterExpressions.push(`contains(${attrName}, ${attrValue})`);
          expressionAttributeValues[attrValue] = value.replace(/\*/g, '');
        } else {
          // Exact match
          filterExpressions.push(`${attrName} = ${attrValue}`);
          expressionAttributeValues[attrValue] = value;
        }

        expressionAttributeNames[attrName] = key;
      });

      if (filterExpressions.length > 0) {
        scanOptions.FilterExpression = filterExpressions.join(' AND ');
        scanOptions.ExpressionAttributeNames = expressionAttributeNames;
        scanOptions.ExpressionAttributeValues = expressionAttributeValues;
      }
    }

    const items = await this.scan(scanOptions);

    // Sort items (DynamoDB doesn't support sorting on scan)
    items.sort((a, b) => {
      const aVal = a[sort];
      const bVal = b[sort];

      if (order === 'desc') {
        return bVal > aVal ? 1 : -1;
      } else {
        return aVal > bVal ? 1 : -1;
      }
    });

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedItems = items.slice(startIndex, endIndex);

    return {
      leads: paginatedItems,
      pagination: {
        total: items.length,
        page,
        limit,
        totalPages: Math.ceil(items.length / limit),
        hasNext: endIndex < items.length,
        hasPrev: page > 1
      }
    };
  }

  /**
   * Update lead score
   * @param {string} leadId - Lead ID
   * @param {number} score - New score (0-100)
   * @returns {Promise<Object>} Updated lead
   */
  async updateScore(leadId, score) {
    if (score < 0 || score > 100) {
      throw new ValidationError('Score must be between 0 and 100');
    }

    logger.info('Updating lead score', { leadId, score });

    return await this.update(leadId, { score });
  }

  /**
   * Update email status
   * @param {string} leadId - Lead ID
   * @param {string} status - New email status
   * @returns {Promise<Object>} Updated lead
   */
  async updateEmailStatus(leadId, status) {
    const validStatuses = ['pending', 'sent', 'opened', 'replied', 'bounced'];
    if (!validStatuses.includes(status)) {
      throw new ValidationError(`Invalid email status. Must be one of: ${validStatuses.join(', ')}`);
    }

    logger.info('Updating lead email status', { leadId, status });

    return await this.update(leadId, { emailStatus: status });
  }

  /**
   * Add tags to lead
   * @param {string} leadId - Lead ID
   * @param {Array<string>} newTags - Tags to add
   * @returns {Promise<Object>} Updated lead
   */
  async addTags(leadId, newTags) {
    const lead = await this.get(leadId);
    const existingTags = lead.tags || [];
    const uniqueTags = [...new Set([...existingTags, ...newTags])];

    logger.info('Adding tags to lead', { leadId, newTags, totalTags: uniqueTags.length });

    return await this.update(leadId, { tags: uniqueTags });
  }

  /**
   * Remove tags from lead
   * @param {string} leadId - Lead ID
   * @param {Array<string>} tagsToRemove - Tags to remove
   * @returns {Promise<Object>} Updated lead
   */
  async removeTags(leadId, tagsToRemove) {
    const lead = await this.get(leadId);
    const existingTags = lead.tags || [];
    const filteredTags = existingTags.filter(tag => !tagsToRemove.includes(tag));

    logger.info('Removing tags from lead', { leadId, tagsToRemove, remainingTags: filteredTags.length });

    return await this.update(leadId, { tags: filteredTags });
  }
}

// Export singleton instance
const leadService = new LeadService();

module.exports = {
  LeadService,
  leadService,
  leadSchema,
  updateLeadSchema
};