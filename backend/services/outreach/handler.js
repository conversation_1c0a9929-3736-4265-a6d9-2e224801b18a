const Joi = require('joi');
const {
  lambdaWrapper,
  parseBody,
  getPathParameters,
  successResponse,
  errorResponse,
  validateInput,
  ValidationError,
  logger
} = require('../shared/utils/api');
const { emailService } = require('../shared/utils/email');
const { smsService } = require('../shared/utils/sms');
const { campaignService } = require('../shared/models/campaign');
const { leadService } = require('../shared/models/lead');

// Validation schemas
const campaignExecutionSchema = Joi.object({
  campaignId: Joi.string().required(),
  leadIds: Joi.array().items(Joi.string()).optional(),
  channels: Joi.array().items(Joi.string().valid('email', 'sms', 'linkedin')).optional(),
  schedule: Joi.string().valid('immediate', 'scheduled').default('immediate'),
  scheduledAt: Joi.date().optional()
});

const sendEmailSchema = Joi.object({
  to: Joi.alternatives().try(
    Joi.string().email(),
    Joi.array().items(Joi.string().email())
  ).required(),
  subject: Joi.string().required(),
  html: Joi.string().optional(),
  text: Joi.string().optional(),
  templateId: Joi.string().optional(),
  templateData: Joi.object().optional()
});

const sendSMSSchema = Joi.object({
  to: Joi.string().required(),
  message: Joi.string().required().max(1600),
  from: Joi.string().optional()
});

const campaignHandler = async (event) => {
  const body = parseBody(event);
  const validatedData = validateInput(body, campaignExecutionSchema);

  const { campaignId, leadIds, channels, schedule, scheduledAt } = validatedData;

  logger.info('Executing campaign', { campaignId, leadCount: leadIds?.length, channels, schedule });

  try {
    // Get campaign details
    const campaign = await campaignService.getCampaign(campaignId);

    // Get leads to process
    const leadsToProcess = leadIds ?
      await Promise.all(leadIds.map(id => leadService.getLead(id))) :
      await Promise.all(campaign.leads.map(id => leadService.getLead(id)));

    // Process outreach based on schedule
    if (schedule === 'immediate') {
      const results = await processImmediateOutreach(campaign, leadsToProcess, channels);
      return successResponse(results);
    } else {
      const results = await scheduleOutreach(campaign, leadsToProcess, channels, scheduledAt);
      return successResponse(results);
    }
  } catch (error) {
    logger.error('Campaign execution failed', { campaignId, error: error.message });
    throw error;
  }
};

const processImmediateOutreach = async (campaign, leads, channels) => {
  const results = {
    processed: 0,
    success: 0,
    failed: 0,
    errors: []
  };

  const campaignChannels = channels || campaign.channels || ['email'];

  for (const lead of leads) {
    try {
      results.processed++;

      for (const channel of campaignChannels) {
        switch (channel) {
          case 'email':
            await processEmailOutreach(campaign, lead);
            break;
          case 'sms':
            await processSMSOutreach(campaign, lead);
            break;
          case 'linkedin':
            await processLinkedInOutreach(campaign, lead);
            break;
          default:
            logger.warn('Unsupported channel', { channel });
        }
      }

      // Update lead status
      await leadService.updateEmailStatus(lead.leadId, 'sent');
      results.success++;

    } catch (error) {
      results.failed++;
      results.errors.push({
        leadId: lead.leadId,
        error: error.message
      });
      logger.error('Lead outreach failed', { leadId: lead.leadId, error: error.message });
    }
  }

  return results;
};

const processEmailOutreach = async (campaign, lead) => {
  const template = campaign.template || {};
  const subject = template.subject || 'Outreach from AI SDR';
  const html = template.body || lead.outreachMessage || 'Default outreach message';

  // Replace template variables
  const personalizedSubject = replaceTemplateVariables(subject, lead);
  const personalizedHtml = replaceTemplateVariables(html, lead);

  await emailService.sendEmail({
    to: lead.contactEmail,
    subject: personalizedSubject,
    html: personalizedHtml,
    tags: ['campaign', campaign.campaignId]
  });

  logger.info('Email sent successfully', { leadId: lead.leadId, email: lead.contactEmail });
};

const processSMSOutreach = async (campaign, lead) => {
  if (!lead.phone) {
    throw new Error('Lead has no phone number for SMS outreach');
  }

  const template = campaign.template || {};
  const message = template.body || lead.outreachMessage || 'Default SMS message';
  const personalizedMessage = replaceTemplateVariables(message, lead);

  await smsService.sendSMS({
    to: lead.phone,
    message: personalizedMessage
  });

  logger.info('SMS sent successfully', { leadId: lead.leadId, phone: lead.phone });
};

const processLinkedInOutreach = async (campaign, lead) => {
  // LinkedIn outreach is not yet implemented
  logger.warn('LinkedIn outreach not yet implemented', { leadId: lead.leadId });
  throw new Error('LinkedIn outreach is not yet available');
};

const scheduleOutreach = async (campaign, leads, channels, scheduledAt) => {
  // For now, just return scheduled status
  // In a real implementation, this would queue the outreach for later execution
  logger.info('Outreach scheduled', {
    campaignId: campaign.campaignId,
    leadCount: leads.length,
    scheduledAt
  });

  return {
    status: 'scheduled',
    campaignId: campaign.campaignId,
    leadCount: leads.length,
    channels: channels || campaign.channels,
    scheduledAt: scheduledAt || new Date(Date.now() + 60000).toISOString() // Default to 1 minute from now
  };
};

const replaceTemplateVariables = (template, lead) => {
  return template
    .replace(/\{\{companyName\}\}/g, lead.companyName || '')
    .replace(/\{\{contactName\}\}/g, lead.contactName || '')
    .replace(/\{\{industry\}\}/g, lead.industry || '')
    .replace(/\{\{location\}\}/g, lead.location || '');
};

const sendEmailHandler = async (event) => {
  const body = parseBody(event);
  const validatedData = validateInput(body, sendEmailSchema);

  logger.info('Sending individual email', { to: validatedData.to });

  try {
    const result = await emailService.sendEmail(validatedData);
    return successResponse(result);
  } catch (error) {
    logger.error('Failed to send email', { error: error.message });
    throw error;
  }
};

const sendSMSHandler = async (event) => {
  const body = parseBody(event);
  const validatedData = validateInput(body, sendSMSSchema);

  logger.info('Sending individual SMS', { to: validatedData.to });

  try {
    const result = await smsService.sendSMS(validatedData);
    return successResponse(result);
  } catch (error) {
    logger.error('Failed to send SMS', { error: error.message });
    throw error;
  }
};

const getOutreachStatus = async (event) => {
  const { campaignId } = getPathParameters(event);

  if (!campaignId) {
    throw new ValidationError('Campaign ID is required');
  }

  logger.info('Getting outreach status', { campaignId });

  try {
    const campaign = await campaignService.getCampaign(campaignId);

    // Get leads and their statuses
    const leads = await Promise.all(
      campaign.leads.map(async (leadId) => {
        try {
          const lead = await leadService.getLead(leadId);
          return {
            leadId: lead.leadId,
            companyName: lead.companyName,
            contactEmail: lead.contactEmail,
            emailStatus: lead.emailStatus,
            score: lead.score
          };
        } catch (error) {
          return {
            leadId,
            error: 'Lead not found'
          };
        }
      })
    );

    const statusSummary = {
      campaignId,
      campaignName: campaign.name,
      campaignStatus: campaign.status,
      totalLeads: leads.length,
      statusBreakdown: leads.reduce((acc, lead) => {
        const status = lead.emailStatus || 'unknown';
        acc[status] = (acc[status] || 0) + 1;
        return acc;
      }, {}),
      leads
    };

    return successResponse(statusSummary);
  } catch (error) {
    logger.error('Failed to get outreach status', { campaignId, error: error.message });
    throw error;
  }
};

// Main handler for API Gateway routing
const main = lambdaWrapper(async (event, context) => {
  const { httpMethod, path, pathParameters } = event;
  const pathSegments = path.split('/').filter(Boolean);
  const action = pathSegments[pathSegments.length - 1];
  const campaignId = pathParameters?.id;

  logger.info('Outreach service request', { httpMethod, path, action, campaignId });

  switch (httpMethod) {
    case 'POST':
      switch (action) {
        case 'campaign':
        case 'execute':
          return await campaignHandler(event, context);
        case 'email':
          return await sendEmailHandler(event, context);
        case 'sms':
          return await sendSMSHandler(event, context);
        default:
          throw new ValidationError(`Unknown action: ${action}`);
      }
    case 'GET':
      switch (action) {
        case 'status':
          return await getOutreachStatus(event, context);
        default:
          throw new ValidationError(`Unknown action: ${action}`);
      }
    case 'OPTIONS':
      return successResponse({ message: 'CORS preflight' });
    default:
      throw new ValidationError(`Unsupported HTTP method: ${httpMethod}`);
  }
});

// Export wrapped handlers
module.exports = {
  main,
  campaignHandler: lambdaWrapper(campaignHandler),
  sendEmailHandler: lambdaWrapper(sendEmailHandler),
  sendSMSHandler: lambdaWrapper(sendSMSHandler),
  getOutreachStatus: lambdaWrapper(getOutreachStatus)
};