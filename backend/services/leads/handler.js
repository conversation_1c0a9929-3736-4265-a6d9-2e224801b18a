const Joi = require('joi');
const {
  lambdaWrapper,
  parseBody,
  getPathParameters,
  getQueryParameters,
  successResponse,
  errorResponse,
  validateInput,
  ValidationError,
  logger
} = require('../shared/utils/api');
const { leadService } = require('../shared/models/lead');

// Validation schemas
const importLeadsSchema = Joi.object({
  leads: Joi.array().items(Joi.object({
    companyName: Joi.string().required(),
    contactEmail: Joi.string().email().required(),
    contactName: Joi.string().optional(),
    phone: Joi.string().optional(),
    revenue: Joi.number().positive().optional(),
    industry: Joi.string().optional(),
    location: Joi.string().optional(),
    expansionPlans: Joi.string().optional(),
    source: Joi.string().optional(),
    tags: Joi.array().items(Joi.string()).optional()
  })).required().min(1)
});

const updateLeadSchema = Joi.object({
  companyName: Joi.string().optional(),
  contactEmail: Joi.string().email().optional(),
  contactName: Joi.string().optional(),
  phone: Joi.string().optional(),
  revenue: Joi.number().positive().optional(),
  industry: Joi.string().optional(),
  location: Joi.string().optional(),
  expansionPlans: Joi.string().optional(),
  score: Joi.number().min(0).max(100).optional(),
  outreachMessage: Joi.string().optional(),
  emailStatus: Joi.string().valid('pending', 'sent', 'opened', 'replied', 'bounced').optional(),
  tags: Joi.array().items(Joi.string()).optional(),
  notes: Joi.string().optional(),
  assignedTo: Joi.string().optional()
});

const importLeads = async (event) => {
  const body = parseBody(event);
  const validatedData = validateInput(body, importLeadsSchema);

  const { leads } = validatedData;

  logger.info('Importing leads', { count: leads.length });

  try {
    const importedLeads = [];
    const errors = [];

    // Process each lead
    for (let i = 0; i < leads.length; i++) {
      try {
        const lead = await leadService.createLead(leads[i]);
        importedLeads.push(lead);
      } catch (error) {
        errors.push({
          index: i,
          lead: leads[i],
          error: error.message
        });
      }
    }

    logger.info('Lead import completed', {
      imported: importedLeads.length,
      errors: errors.length
    });

    return successResponse({
      message: 'Lead import completed',
      imported: importedLeads.length,
      errors: errors.length,
      leads: importedLeads,
      importErrors: errors
    });
  } catch (error) {
    logger.error('Lead import failed', { error: error.message });
    throw error;
  }
};

const getLeads = async (event) => {
  const queryParams = getQueryParameters(event);

  // Parse query parameters
  const options = {
    page: parseInt(queryParams.page) || 1,
    limit: parseInt(queryParams.limit) || 20,
    sort: queryParams.sort || 'createdAt',
    order: queryParams.order || 'desc',
    filter: {}
  };

  // Add filters
  if (queryParams.status) options.filter.emailStatus = queryParams.status;
  if (queryParams.industry) options.filter.industry = queryParams.industry;
  if (queryParams.assignedTo) options.filter.assignedTo = queryParams.assignedTo;
  if (queryParams.search) options.filter.companyName = `*${queryParams.search}*`;

  logger.info('Fetching leads', options);

  try {
    const result = await leadService.getLeads(options);

    return successResponse(result);
  } catch (error) {
    logger.error('Failed to fetch leads', { error: error.message });
    throw error;
  }
};

const getLeadById = async (event) => {
  const { id } = getPathParameters(event);

  if (!id) {
    throw new ValidationError('Lead ID is required');
  }

  logger.info('Fetching lead by ID', { leadId: id });

  try {
    const lead = await leadService.getLead(id);

    return successResponse(lead);
  } catch (error) {
    logger.error('Failed to fetch lead', { leadId: id, error: error.message });
    throw error;
  }
};

const updateLead = async (event) => {
  const { id } = getPathParameters(event);
  const body = parseBody(event);

  if (!id) {
    throw new ValidationError('Lead ID is required');
  }

  const validatedUpdates = validateInput(body, updateLeadSchema);

  logger.info('Updating lead', { leadId: id, updates: Object.keys(validatedUpdates) });

  try {
    const updatedLead = await leadService.updateLead(id, validatedUpdates);

    return successResponse(updatedLead);
  } catch (error) {
    logger.error('Failed to update lead', { leadId: id, error: error.message });
    throw error;
  }
};

const deleteLead = async (event) => {
  const { id } = getPathParameters(event);

  if (!id) {
    throw new ValidationError('Lead ID is required');
  }

  logger.info('Deleting lead', { leadId: id });

  try {
    await leadService.deleteLead(id);

    return successResponse({ message: 'Lead deleted successfully' }, 204);
  } catch (error) {
    logger.error('Failed to delete lead', { leadId: id, error: error.message });
    throw error;
  }
};

const createLead = async (event) => {
  const body = parseBody(event);

  logger.info('Creating new lead');

  try {
    const lead = await leadService.createLead(body);

    return successResponse(lead, 201);
  } catch (error) {
    logger.error('Failed to create lead', { error: error.message });
    throw error;
  }
};

// Main handler for API Gateway routing
const main = lambdaWrapper(async (event, context) => {
  const { httpMethod, path, pathParameters } = event;
  const pathSegments = path.split('/').filter(Boolean);
  const action = pathSegments[pathSegments.length - 1];
  const leadId = pathParameters?.id;

  logger.info('Leads service request', { httpMethod, path, action, leadId });

  switch (httpMethod) {
    case 'GET':
      if (leadId) {
        return await getLeadById(event, context);
      } else {
        return await getLeads(event, context);
      }
    case 'POST':
      if (action === 'import') {
        return await importLeads(event, context);
      } else {
        return await createLead(event, context);
      }
    case 'PUT':
    case 'PATCH':
      if (!leadId) {
        throw new ValidationError('Lead ID is required for updates');
      }
      return await updateLead(event, context);
    case 'DELETE':
      if (!leadId) {
        throw new ValidationError('Lead ID is required for deletion');
      }
      return await deleteLead(event, context);
    case 'OPTIONS':
      return successResponse({ message: 'CORS preflight' });
    default:
      throw new ValidationError(`Unsupported HTTP method: ${httpMethod}`);
  }
});

// Export wrapped handlers
module.exports = {
  main,
  importLeads: lambdaWrapper(importLeads),
  getLeads: lambdaWrapper(getLeads),
  getLeadById: lambdaWrapper(getLeadById),
  updateLead: lambdaWrapper(updateLead),
  deleteLead: lambdaWrapper(deleteLead),
  createLead: lambdaWrapper(createLead)
};