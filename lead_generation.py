import requests
from bs4 import BeautifulSoup
import sqlite3
import pandas as pd

def scrape_leads(url):
    response = requests.get(url)
    soup = BeautifulSoup(response.text, 'html.parser')
    leads = []
    # Example: Extract company data from a directory
    for item in soup.select('.company-listing'):
        name = item.select_one('.company-name').text
        email = item.select_one('.contact-email').text if item.select_one('.contact-email') else 'N/A'
        revenue = item.select_one('.revenue').text if item.select_one('.revenue') else 'N/A'
        leads.append({'name': name, 'email': email, 'revenue': revenue})
    return leads

def filter_leads(leads):
    df = pd.DataFrame(leads)
    # Filter by revenue (example: $10M-$50M)
    df = df[df['revenue'].str.contains('10M-50M', na=False)]
    return df

def save_to_db(leads):
    conn = sqlite3.connect('leads.db')
    leads.to_sql('leads', conn, if_exists='append', index=False)
    conn.close()

if __name__ == "__main__":
    url = "https://example-mining-directory.com"  # Replace with actual URL
    leads = scrape_leads(url)
    filtered_leads = filter_leads(leads)
    save_to_db(filtered_leads)