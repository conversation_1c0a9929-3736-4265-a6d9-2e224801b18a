const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const Joi = require('joi');
const {
  lambdaWrapper,
  parseBody,
  successResponse,
  errorResponse,
  validateInput,
  UnauthorizedError,
  ValidationError,
  logger
} = require('../shared/utils/api');
const { UserService } = require('./userService');

// Get JWT secret from environment or AWS Secrets Manager
const JWT_SECRET = process.env.JWT_SECRET || 'your-default-secret-change-in-production';

// Validation schemas
const loginSchema = Joi.object({
  username: Joi.string().required().min(3).max(50),
  password: Joi.string().required().min(6)
});

const refreshSchema = Joi.object({
  token: Joi.string().required()
});

const registerSchema = Joi.object({
  username: Joi.string().required().min(3).max(50),
  email: Joi.string().email().required(),
  password: Joi.string().required().min(6).pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]')),
  role: Joi.string().valid('admin', 'user').default('user')
});

const userService = new UserService();

const login = async (event) => {
  const body = parseBody(event);
  const validatedData = validateInput(body, loginSchema);

  const { username, password } = validatedData;

  logger.info('Login attempt', { username });

  try {
    // Get user from database
    const user = await userService.getUserByUsername(username);

    if (!user) {
      logger.warn('Login failed - user not found', { username });
      throw new UnauthorizedError('Invalid credentials');
    }

    // Verify password
    const isMatch = await bcrypt.compare(password, user.passwordHash);

    if (!isMatch) {
      logger.warn('Login failed - invalid password', { username });
      throw new UnauthorizedError('Invalid credentials');
    }

    // Generate JWT token
    const token = jwt.sign(
      {
        id: user.userId,
        username: user.username,
        email: user.email,
        role: user.role
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    logger.info('Login successful', { username, userId: user.userId });

    return successResponse({
      token,
      user: {
        id: user.userId,
        username: user.username,
        email: user.email,
        role: user.role
      }
    });
  } catch (error) {
    if (error instanceof UnauthorizedError) {
      return errorResponse(error, 401);
    }
    throw error;
  }
};

const refresh = async (event) => {
  const body = parseBody(event);
  const validatedData = validateInput(body, refreshSchema);

  const { token } = validatedData;

  logger.info('Token refresh attempt');

  try {
    // Verify the existing token
    const decoded = jwt.verify(token, JWT_SECRET);

    // Check if user still exists and is active
    const user = await userService.getUser(decoded.id);

    if (!user || user.status !== 'active') {
      logger.warn('Token refresh failed - user not found or inactive', { userId: decoded.id });
      throw new UnauthorizedError('User account is not active');
    }

    // Generate new token
    const newToken = jwt.sign(
      {
        id: user.userId,
        username: user.username,
        email: user.email,
        role: user.role
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    logger.info('Token refreshed successfully', { userId: user.userId });

    return successResponse({
      token: newToken,
      user: {
        id: user.userId,
        username: user.username,
        email: user.email,
        role: user.role
      }
    });
  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      logger.warn('Token refresh failed - invalid token');
      throw new UnauthorizedError('Invalid or expired token');
    }
    throw error;
  }
};

const register = async (event) => {
  const body = parseBody(event);
  const validatedData = validateInput(body, registerSchema);

  const { username, email, password, role } = validatedData;

  logger.info('Registration attempt', { username, email });

  try {
    // Check if user already exists
    const existingUser = await userService.getUserByUsername(username);
    if (existingUser) {
      throw new ValidationError('Username already exists');
    }

    const existingEmail = await userService.getUserByEmail(email);
    if (existingEmail) {
      throw new ValidationError('Email already exists');
    }

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Create user
    const userData = {
      username,
      email,
      passwordHash,
      role,
      status: 'active'
    };

    const user = await userService.createUser(userData);

    logger.info('User registered successfully', { userId: user.userId, username });

    return successResponse({
      message: 'User registered successfully',
      user: {
        id: user.userId,
        username: user.username,
        email: user.email,
        role: user.role
      }
    }, 201);
  } catch (error) {
    if (error instanceof ValidationError) {
      return errorResponse(error, 400);
    }
    throw error;
  }
};

const verifyToken = async (event) => {
  const authHeader = event.headers?.Authorization || event.headers?.authorization;

  if (!authHeader) {
    throw new UnauthorizedError('Authorization header is required');
  }

  try {
    const token = authHeader.replace(/^Bearer\s+/, '');
    const decoded = jwt.verify(token, JWT_SECRET);

    // Check if user still exists and is active
    const user = await userService.getUser(decoded.id);

    if (!user || user.status !== 'active') {
      throw new UnauthorizedError('User account is not active');
    }

    return successResponse({
      valid: true,
      user: {
        id: user.userId,
        username: user.username,
        email: user.email,
        role: user.role
      }
    });
  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      throw new UnauthorizedError('Invalid or expired token');
    }
    throw error;
  }
};

// Export wrapped handlers
module.exports = {
  login: lambdaWrapper(login),
  refresh: lambdaWrapper(refresh),
  register: lambdaWrapper(register),
  verifyToken: lambdaWrapper(verifyToken)
};