{"name": "ai-sdr-agent", "version": "1.0.0", "description": "AI Sales Development Representative agent for mining equipment suppliers", "main": "index.js", "scripts": {"install:all": "npm run install:frontend && npm run install:backend", "install:frontend": "cd src/frontend && npm install", "install:backend": "cd src/backend && npm install", "start": "npm run start:frontend", "start:frontend": "cd src/frontend && npm start", "start:backend": "cd src/backend && serverless offline", "build": "npm run build:frontend", "build:frontend": "cd src/frontend && npm run build", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd src/frontend && npm test", "test:backend": "cd src/backend && npm test", "test:e2e": "cd tests && npm run cypress:run", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd src/frontend && npm run lint", "lint:backend": "cd src/backend && npm run lint", "deploy": "npm run deploy:backend && npm run deploy:frontend", "deploy:backend": "cd src/backend && serverless deploy", "deploy:frontend": "cd src/frontend && npm run build && npm run deploy", "clean": "npm run clean:frontend && npm run clean:backend", "clean:frontend": "cd src/frontend && rm -rf node_modules build", "clean:backend": "cd src/backend && rm -rf node_modules .serverless"}, "repository": {"type": "git", "url": "https://github.com/GEMDevEng/AI_SDR.git"}, "keywords": ["ai", "sdr", "sales", "automation", "mining", "equipment", "lead-generation", "crm", "serverless", "aws"], "author": "GEMDevEng <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/GEMDevEng/AI_SDR/issues"}, "homepage": "https://github.com/GEMDevEng/AI_SDR#readme", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "workspaces": ["src/frontend", "src/backend", "tests"], "devDependencies": {"concurrently": "^8.2.0", "cross-env": "^7.0.3"}, "dependencies": {"@hubspot/api-client": "^13.0.0", "@sendgrid/mail": "^8.1.5", "twilio": "^5.7.1", "winston": "^3.17.0"}}