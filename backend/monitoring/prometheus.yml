# AI SDR Backend - Prometheus Configuration
# Monitoring and metrics collection

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'ai-sdr-monitor'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Load rules once and periodically evaluate them
rule_files:
  - "alert_rules.yml"

# Scrape configurations
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # Node.js API Backend
  - job_name: 'ai-sdr-api'
    static_configs:
      - targets: ['api:3001']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # Python Lead Enrichment Service
  - job_name: 'lead-enrichment'
    static_configs:
      - targets: ['lead-enrichment:5000']
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s

  # Nginx Metrics (if nginx-prometheus-exporter is installed)
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:9113']
    scrape_interval: 30s
    metrics_path: /metrics

  # Redis Metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
    metrics_path: /metrics

  # Node Exporter for system metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # DynamoDB Local (if metrics are exposed)
  - job_name: 'dynamodb'
    static_configs:
      - targets: ['dynamodb:8000']
    scrape_interval: 60s
    metrics_path: /metrics

# Remote write configuration (for production)
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint.com/api/v1/write"
#     basic_auth:
#       username: "username"
#       password: "password"

# Storage configuration
storage:
  tsdb:
    retention.time: 15d
    retention.size: 10GB
