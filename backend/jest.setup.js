// Global test setup
const TestHelpers = require('./services/shared/utils/testHelpers');

// Setup test environment globally
beforeAll(() => {
  TestHelpers.setupTestEnvironment();
});

// Cleanup after all tests
afterAll(() => {
  TestHelpers.cleanupTestEnvironment();
});

// Setup before each test
beforeEach(() => {
  // Clear all mocks before each test
  jest.clearAllMocks();
});

// Global test utilities
global.TestHelpers = TestHelpers;

// Suppress console logs during tests unless explicitly needed
const originalConsole = console;
global.console = {
  ...originalConsole,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};

// Restore console for specific tests if needed
global.restoreConsole = () => {
  global.console = originalConsole;
};

// Mock timers
jest.useFakeTimers();

// Increase timeout for async operations
jest.setTimeout(30000);
