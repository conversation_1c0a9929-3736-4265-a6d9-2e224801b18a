const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();
const TABLE_NAME = process.env.CAMPAIGNS_TABLE;

class Campaign {
  static async create(campaignData) {
    const params = {
      TableName: TABLE_NAME,
      Item: {
        campaignId: campaignData.campaignId || AWS.util.uuid.v4(),
        name: campaignData.name,
        status: campaignData.status || 'draft',
        channels: campaignData.channels || [],
        schedule: campaignData.schedule || 'immediate',
        leads: campaignData.leads || [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    };

    await dynamodb.put(params).promise();
    return params.Item;
  }

  static async get(campaignId) {
    const params = {
      TableName: TABLE_NAME,
      Key: { campaignId }
    };
    const result = await dynamodb.get(params).promise();
    return result.Item;
  }

  static async updateStatus(campaignId, status) {
    const params = {
      TableName: TABLE_NAME,
      Key: { campaignId },
      UpdateExpression: 'SET #status = :status, updatedAt = :updatedAt',
      ExpressionAttributeNames: {
        '#status': 'status'
      },
      ExpressionAttributeValues: {
        ':status': status,
        ':updatedAt': new Date().toISOString()
      },
      ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();
    return result.Attributes;
  }

  static async addLeads(campaignId, leadIds) {
    const params = {
      TableName: TABLE_NAME,
      Key: { campaignId },
      UpdateExpression: 'SET leads = list_append(leads, :newLeads), updatedAt = :updatedAt',
      ExpressionAttributeValues: {
        ':newLeads': leadIds,
        ':updatedAt': new Date().toISOString()
      },
      ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();
    return result.Attributes;
  }
}

module.exports = Campaign;