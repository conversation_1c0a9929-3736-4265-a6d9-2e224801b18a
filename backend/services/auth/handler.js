const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

// This would typically be in a separate config file
const JWT_SECRET = process.env.JWT_SECRET || 'your-default-secret';

// Placeholder for database interaction
const users = []; // In a real app, this would be a database call

const login = async (event) => {
  const { username, password } = JSON.parse(event.body);

  // In a real app, you would fetch the user from DynamoDB
  const user = users.find(u => u.username === username);

  if (!user) {
    return {
      statusCode: 401,
      body: JSON.stringify({ message: 'Invalid credentials' }),
    };
  }

  const isMatch = await bcrypt.compare(password, user.password_hash);

  if (!isMatch) {
    return {
      statusCode: 401,
      body: JSON.stringify({ message: 'Invalid credentials' }),
    };
  }

  const token = jwt.sign({ id: user.user_id, role: user.role }, JWT_SECRET, { expiresIn: '1h' });

  return {
    statusCode: 200,
    body: JSON.stringify({ token }),
  };
};

const refresh = async (event) => {
  const { token } = JSON.parse(event.body);

  if (!token) {
    return {
      statusCode: 401,
      body: JSON.stringify({ message: 'Token is required' }),
    };
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    const newToken = jwt.sign({ id: decoded.id, role: decoded.role }, JWT_SECRET, { expiresIn: '1h' });

    return {
      statusCode: 200,
      body: JSON.stringify({ token: newToken }),
    };
  } catch (error) {
    return {
      statusCode: 401,
      body: JSON.stringify({ message: 'Invalid token' }),
    };
  }
};

module.exports = {
  login,
  refresh,
};