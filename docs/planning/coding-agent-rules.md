# Project Rules for AI Coding Agents

## 1. Introduction
This document establishes the rules and best practices for AI coding agents, such as [Google’s <PERSON>](https://blog.google/technology/google-labs/jules/), [<PERSON><PERSON>](https://cline.bot/), and [Refact.ai](https://refact.ai/), contributing to the AI Sales Development Representative (SDR) Agent project. The AI SDR Agent is a modular SaaS platform designed to automate lead generation, qualification, outreach, and analytics for mining equipment suppliers, targeting mid-tier companies with revenues of $10-50 million in regions like Africa and North America. The project emphasizes cost efficiency by leveraging free or low-cost tools, and AI coding agents play a critical role in generating high-quality, scalable, and secure code.

## 2. Objectives
AI coding agents are expected to:
- Generate code for core services, including Authentication, Lead Management, Outreach Sequencer, AI Agents Service, Scheduling, and Analytics.
- Ensure code aligns with the project’s serverless, event-driven microservices architecture.
- Optimize for performance and cost within free-tier limits of services like [SendGrid](https://sendgrid.com/) and [AWS](https://aws.amazon.com/).
- Facilitate seamless integration with external APIs and databases.
- Produce well-documented, tested, and maintainable code that supports future scalability.

**Key Deliverables**:
- Modular code for backend services and API integrations.
- Comprehensive unit and integration tests.
- Clear documentation for all code and APIs.

## 3. Tools and Technologies
AI coding agents must use the following technologies, as outlined in the project’s tech stack:

| **Category**         | **Technology**                                                                 | **Purpose**                                                                 |
|----------------------|-------------------------------------------------------------------------------|-----------------------------------------------------------------------------|
| AI Coding Agents     | [Google’s Jules](https://blog.google/technology/google-labs/jules/), [Cline](https://cline.bot/), [Refact.ai](https://refact.ai/) | Code generation and refinement within repositories.                         |
| Programming Languages| [Node.js](https://nodejs.org/) (v18.x), [Python](https://www.python.org/) (v3.10+) | Backend service development.                                                |
| Frontend             | [React.js](https://reactjs.org/) (v18.x), [Tailwind CSS](https://tailwindcss.com/) | Building responsive user interfaces.                                       |
| Cloud Services       | [AWS Lambda](https://aws.amazon.com/lambda/), [API Gateway](https://aws.amazon.com/api-gateway/), [DynamoDB](https://aws.amazon.com/dynamodb/), [SQS](https://aws.amazon.com/sqs/) | Serverless computing, API management, database, and message queuing.        |
| AI Services          | [Relevance AI](https://relevanceai.com/agents), [Google Gemini API](https://ai.google.dev/gemini-api) | Lead scoring and personalized content generation.                           |
| Communication        | [SendGrid](https://sendgrid.com/), [Twilio](https://www.twilio.com/), [LinkedIn Marketing API](https://www.linkedin.com/developers/) | Email, SMS, and LinkedIn outreach.                                         |
| Scheduling           | [Calendly](https://calendly.com/), [Google Calendar API](https://developers.google.com/calendar/api) | Meeting scheduling and calendar integration.                                |
| Deployment           | [Serverless Framework](https://www.serverless.com/), [GitHub Actions](https://github.com/features/actions) | Automating deployment and CI/CD pipelines.                                  |

## 4. Coding Standards
- **Language-Specific Standards**:
  - Python: Adhere to [PEP 8](https://peps.python.org/pep-0008/) for style and formatting.
  - JavaScript/Node.js: Use [ESLint](https://eslint.org/) with standard configurations.
- **Code Organization**:
  - Structure code into modular directories (e.g., `/services`, `/utils`, `/tests`).
  - Separate concerns (e.g., business logic, API handlers, utilities).
- **Naming Conventions**:
  - Use camelCase for JavaScript and snake_case for Python.
  - Choose descriptive names for variables, functions, and files (e.g., `sendOutreachEmail`, `lead_scoring_service`).
- **Commenting**:
  - Include inline comments for complex logic.
  - Use [JSDoc](https://jsdoc.app/) for JavaScript and [docstrings](https://www.python.org/dev/peps/pep-0257/) for Python to document functions and classes.

## 5. Security Practices
- **Authentication**:
  - Implement [JSON Web Tokens (JWT)](https://jwt.io/) for secure, stateless authentication.
  - Store tokens in HTTP-only cookies.
- **Authorization**:
  - Use role-based access control (RBAC) to restrict access (e.g., admin vs. user roles).
- **Sensitive Data**:
  - Store API keys and credentials in [AWS Secrets Manager](https://aws.amazon.com/secrets-manager/).
  - Avoid hardcoding sensitive information.
- **Data Transmission**:
  - Ensure all API communications use [HTTPS](https://en.wikipedia.org/wiki/HTTPS).
- **Input Validation**:
  - Sanitize and validate all user inputs to prevent injection attacks (e.g., SQL injection, XSS).

## 6. Integration Guidelines
- **External APIs**:
  - Integrate with [Relevance AI](https://relevanceai.com/agents), [Google Gemini API](https://ai.google.dev/gemini-api), [SendGrid](https://sendgrid.com/), [Twilio](https://www.twilio.com/), and [LinkedIn Marketing API](https://www.linkedin.com/developers/).
  - Respect API rate limits (e.g., SendGrid: 100 emails/day, Relevance AI: 100 credits/day).
  - Implement retry mechanisms for transient failures.
- **Database**:
  - Use [AWS DynamoDB](https://aws.amazon.com/dynamodb/) for storing leads, campaigns, and user data.
  - Optimize queries to avoid performance bottlenecks (e.g., use appropriate partition keys).
- **Message Queuing**:
  - Use [AWS SQS](https://aws.amazon.com/sqs/) for asynchronous tasks like email sending and lead enrichment.
  - Implement dead-letter queues for failed messages.

## 7. Testing and Validation
- **Unit Testing**:
  - Write tests for all functions and services using [Jest](https://jestjs.io/) for JavaScript and [unittest](https://docs.python.org/3/library/unittest.html) for Python.
- **Integration Testing**:
  - Test interactions between services (e.g., Lead Management and Outreach Sequencer).
- **End-to-End Testing**:
  - Use [Cypress](https://www.cypress.io/) to validate user flows (e.g., lead import, campaign execution).
- **Code Coverage**:
  - Target at least 80% code coverage for unit tests.
- **Mocking**:
  - Use [Sinon.js](https://sinonjs.org/) or [unittest.mock](https://docs.python.org/3/library/unittest.mock.html) to mock external APIs during testing.

## 8. Documentation
- **Code Documentation**:
  - Document all functions, classes, and APIs using JSDoc or docstrings.
- **API Documentation**:
  - Provide detailed documentation for all endpoints, including request/response formats and authentication requirements.
- **User Guides**:
  - Include guides for key features (e.g., importing leads, creating campaigns) to assist users.

## 9. Collaboration
- **Version Control**:
  - Use [Git](https://git-scm.com/) and [GitHub](https://github.com/) for code management.
  - Follow a branching strategy (e.g., feature branches with pull requests).
- **Code Reviews**:
  - All AI-generated code must be reviewed by human developers for quality and compliance.
- **Task Management**:
  - Use [GitHub Issues](https://github.com/features/issues) or [Trello](https://trello.com/) to track tasks and progress.

## 10. Monitoring and Logging
- **Logging**:
  - Implement logging with [AWS CloudWatch](https://aws.amazon.com/cloudwatch/) for all services.
  - Log critical events, errors, and performance metrics.
- **Monitoring**:
  - Set up alerts for errors, performance degradation, and resource usage.
- **Tracing**:
  - Use [AWS X-Ray](https://aws.amazon.com/xray/) for distributed tracing of microservices.

## 11. Deployment
- **Deployment Tool**:
  - Use [Serverless Framework](https://www.serverless.com/) to define and deploy serverless functions.
- **CI/CD**:
  - Automate testing, building, and deployment with [GitHub Actions](https://github.com/features/actions).
- **Infrastructure**:
  - Provision AWS resources using [AWS CloudFormation](https://aws.amazon.com/cloudformation/) or [Terraform](https://www.terraform.io/).

## 12. Maintenance and Updates
- **Bug Fixes**:
  - Prioritize critical bugs and security vulnerabilities.
- **Feature Updates**:
  - Follow the phased implementation plan (Phase 1: Core Outreach, Phase 2: Additional Channels, Phase 3: Advanced AI).
- **Dependency Management**:
  - Regularly update dependencies to address security and performance issues.

## 13. Cost Efficiency
- **Free Tier Optimization**:
  - Optimize code to stay within free tier limits (e.g., SendGrid: 100 emails/day, Relevance AI: 100 credits/day).
- **Resource Optimization**:
  - Minimize Lambda execution times and optimize DynamoDB queries.
- **Caching**:
  - Use [AWS ElastiCache](https://aws.amazon.com/elasticache/) for frequently accessed data to reduce costs.

## 14. Scalability
- **Horizontal Scaling**:
  - Design services to scale independently using serverless functions.
- **Database Scalability**:
  - Leverage DynamoDB’s automatic scaling features.
- **Message Queue Scalability**:
  - Use SQS to handle high volumes of asynchronous tasks.

## 15. Compliance
- **Data Protection**:
  - Adhere to [GDPR](https://gdpr.eu/) for handling user data.
- **Email Deliverability**:
  - Ensure compliance with email regulations to avoid spam filters.
- **Industry Adaptability**:
  - Design code to be adaptable for other industries in future phases.

## 16. Specific Implementations
AI coding agents must focus on the following tasks, aligned with the project’s phased implementation plan:

| **Phase** | **Task** | **Description** |
|-----------|----------|-----------------|
| Phase 1: Core Outreach | Authentication Service | Implement JWT-based authentication with user roles. |
| Phase 1: Core Outreach | Lead Management Service | Develop CRUD operations for leads, integrating with DynamoDB and data enrichment APIs (e.g., [Apollo.io](https://www.apollo.io/)). |
| Phase 1: Core Outreach | Outreach Sequencer | Implement email outreach using SendGrid, with basic campaign management. |
| Phase 2: Additional Channels | Multi-Channel Outreach | Add LinkedIn and SMS support using LinkedIn Marketing API and Twilio. |
| Phase 2: Additional Channels | Lead Enrichment | Integrate with Apollo.io or Clearbit for enhanced lead data. |
| Phase 3: Advanced AI | AI Agents Service | Enhance lead scoring and content generation with Relevance AI and Google Gemini API. |
| Phase 3: Advanced AI | Analytics Service | Develop dashboards for campaign performance metrics. |

## 17. Using AI Coding Agents
- **Effective Usage**:
  - Leverage Jules for repository-based code generation, Cline for VS Code integration, and Refact.ai for open-source coding tasks.
- **Prompting**:
  - Provide clear, detailed prompts specifying requirements, constraints, and context (e.g., “Generate a Node.js Lambda function for sending emails via SendGrid”).
- **Review and Validation**:
  - All AI-generated code must be reviewed by human developers to ensure quality, security, and compliance.
- **Task Allocation**:
  - Assign tasks based on agent strengths (e.g., Jules for backend services, Cline for frontend components).
- **Human Oversight**:
  - Ensure human developers oversee critical logic, security-sensitive code, and architectural decisions.

## 18. Evaluation Criteria
- **Code Quality**: Assessed based on adherence to coding standards, readability, and modularity.
- **Functionality**: Code must meet the functional requirements outlined in the SRS and PRD.
- **Performance**: Code should optimize for low latency and resource usage.
- **Security**: Compliance with security practices, including secure data handling and input validation.
- **Documentation**: Completeness and clarity of code and API documentation.

## Conclusion
These project rules ensure that AI coding agents generate high-quality, secure, and efficient code for the AI SDR Agent, aligning with its modular architecture and cost-efficient design. By following these guidelines, agents will contribute to a scalable, maintainable solution that supports mining equipment suppliers in automating their sales processes. Human oversight and collaboration are critical to validate AI-generated code and ensure project success.