# Building an AI SDR Agent with Free Tools Using VS Code

## Introduction
This guide provides a comprehensive, step-by-step procedure to build an AI Sales Development Representative (SDR) Agent using free tools and Visual Studio Code (VS Code). The agent automates lead generation, scoring, personalized outreach, and email communication, tailored for industries like mining equipment suppliers, distributors, service companies, and mining companies with revenues of $10-50 million in regions like Africa and North America. The design prioritizes efficiency and functionality within a $0 budget, using open-source tools and free tiers of services, with low-cost alternatives suggested only when necessary.

## Prerequisites
- **Basic Python Knowledge**: Familiarity with Python programming for scripting and data manipulation.
- **Web Scraping and API Understanding**: Basic knowledge of web scraping techniques and API integration.
- **VS Code**: Installed from [code.visualstudio.com](https://code.visualstudio.com/).
- **Free Accounts**: Sign up for free tiers of SendGrid ([sendgrid.com/free/](https://sendgrid.com/free/)) and Hugging Face ([huggingface.co/docs/api-inference/index](https://huggingface.co/docs/api-inference/index)) for email delivery and AI text generation, respectively.

## Core Components
The AI SDR Agent consists of four modular components, each addressing a critical sales task. These components are interconnected to form a cohesive workflow:

| **Component**                     | **Function**                                                                 | **Input**                          | **Output**                          | **Interrelation**                                                                 |
|-----------------------------------|-----------------------------------------------------------------------------|------------------------------------|-------------------------------------|-----------------------------------------------------------------------------------|
| Automated Lead Generation         | Collects data on potential leads from public sources.                        | Public data sources (e.g., websites, directories) | Lead data (company name, contact info, etc.) | Feeds leads into the scoring component.                                           |
| Intelligent Lead Scoring          | Prioritizes leads based on predefined criteria (e.g., revenue, location).    | Lead data from database            | Scored and ranked leads             | Passes high-scoring leads to outreach generation.                                 |
| Personalized Outreach Generation  | Creates customized email messages for each lead.                             | Scored leads                       | Personalized email content           | Sends generated emails to the communication component.                            |
| Email Communication              | Sends emails to leads and tracks delivery (if possible).                     | Personalized email content          | Sent emails, optional tracking data | Executes outreach and can feed response data back to update lead scores/statuses. |

## Step-by-Step Implementation

### Step 0: Understand the Sales Process Manually
- **Objective**: Gain a deep understanding of the sales process before automating to ensure effective strategies.
- **Actions**:
  - Manually research potential leads (e.g., mining companies in Africa) to identify effective sources and data points.
  - Test outreach messages to understand what resonates with your target audience (e.g., emphasizing equipment efficiency).
  - Document the process to define clear automation goals.
- **Why**: Ensures the AI replicates proven strategies, avoiding robotic or ineffective outputs.

### Step 1: Setup Development Environment
- **Objective**: Prepare VS Code for development.
- **Actions**:
  1. Install VS Code from [code.visualstudio.com](https://code.visualstudio.com/).
  2. Install the Python extension in VS Code (search “Python” in Extensions).
  3. Create a project folder (e.g., `ai_sdr_agent`) and initialize a Git repository for version control ([git-scm.com](https://git-scm.com/)).
  4. Install Python ([python.org](https://www.python.org/)) and required libraries: `pip install requests beautifulsoup4 pandas sendgrid`.
- **Why**: A well-configured environment ensures smooth coding and debugging.

### Step 2: Automated Lead Generation
- **Objective**: Collect lead data from public sources, focusing on mining industry targets.
- **Tools**:
  - **Python Libraries**: BeautifulSoup ([crummy.com/software/BeautifulSoup/](https://www.crummy.com/software/BeautifulSoup/)) for web scraping, pandas for data manipulation.
  - **Database**: SQLite (built into Python, [sqlite.org](https://www.sqlite.org/)).
  - **Optional**: Free APIs like Hunter.io (25 free email searches/month, [hunter.io](https://hunter.io/)).
- **Actions**:
  1. **Identify Sources**: Research public sources like mining industry directories or trade publications (e.g., Mining Association of Canada, [mining.ca](https://mining.ca/)).
  2. **Scrape Data**: Write a Python script to extract company name, contact email, revenue, and location.
  3. **Filter Leads**: Use pandas to filter leads based on criteria (e.g., revenue $10-50M, locations like DRC or Canada).
  4. **Store Leads**: Save data in an SQLite database.
- **Example Code**:
  ```python
  import requests
  from bs4 import BeautifulSoup
  import sqlite3
  import pandas as pd

  def彼此

  def scrape_leads(url):
      response = requests.get(url)
      soup = BeautifulSoup(response.text, 'html.parser')
      leads = []
      for item in soup.select('.company-listing'):
          name = item.select_one('.company-name').text
          email = item.select_one('.contact-email').text if item.select_one('.contact-email') else 'N/A'
          revenue = item.select_one('.revenue').text if item.select_one('.revenue') else 'N/A'
          location = item.select_one('.location').text if item.select_one('.location') else 'N/A'
          leads.append({'name': name, 'email': email, 'revenue': revenue, 'location': location})
      return leads

  def filter_leads(leads):
      df = pd.DataFrame(leads)
      df = df[df['revenue'].str.contains('10M-50M', na=False)]
      return df

  def save_to_db(leads):
      conn = sqlite3.connect('leads.db')
      leads.to_sql('leads', conn, if_exists='append', index=False)
      conn.close()

  if __name__ == "__main__":
      url = "https://example.com/mining-directory"  # Replace with actual URL
      leads = scrape_leads(url)
      filtered_leads = filter_leads(leads)
      save_to_db(filtered_leads)
  ```
- **Notes**: Ensure compliance with website terms of service and data privacy laws (e.g., GDPR). Start with manual data collection if scraping is complex.

### Step 3: Intelligent Lead Scoring
- **Objective**: Prioritize leads based on criteria like revenue and location.
- **Tools**: Python, pandas for data processing.
- **Actions**:
  1. **Define Criteria**: Assign points (e.g., +10 for revenue >$20M, +5 for African operations).
  2. **Calculate Scores**: Write a script to score leads and rank them.
  3. **Update Database**: Store scores for high-priority leads.
- **Example Code**:
  ```python
  import sqlite3
  import pandas as pd

  def score_leads():
      conn = sqlite3.connect('leads.db')
      df = pd.read_sql_query("SELECT * FROM leads", conn)
      df['score'] = 0
      df.loc[df['revenue'].str.contains('20M-50M', na=False), 'score'] += 10
      df.loc[df['location'].str.contains('Africa|Canada', na=False), 'score'] += 5
      df = df.sort_values(by='score', ascending=False)
      df.to_sql('scored_leads', conn, if_exists='replace', index=False)
      conn.close()
      return df

  if __name__ == "__main__":
      scored_leads = score_leads()
      print(scored_leads.head())
  ```
- **Notes**: Start with rule-based scoring; consider machine learning (e.g., scikit-learn) later as data grows.

### Step 4: Personalized Outreach Generation
- **Objective**: Create tailored email messages for high-scoring leads.
- **Tools**:
  - **Python**: For string formatting.
  - **Hugging Face API**: Free tier for text generation ([huggingface.co/docs/api-inference](https://huggingface.co/docs/api-inference/index)).
- **Actions**:
  1. **Create Templates**: Design email templates with placeholders (e.g., `{company_name}`).
  2. **Fetch Leads**: Retrieve high-scoring leads from the database.
  3. **Generate Emails**: Use string formatting or Hugging Face API for personalized content.
- **Example Code**:
  ```python
  import sqlite3
  import requests

  API_URL = "https://api-inference.huggingface.co/models/gpt2"
  headers = {"Authorization": "Bearer YOUR_HUGGING_FACE_API_TOKEN"}

  def generate_email(lead):
      template = """Subject: Tailored Solutions for {company_name}
  Dear {contact_name},
  We understand that {company_name} is a leader in {industry}. Our solutions can enhance your operations in {location} by addressing challenges like {specific_need}.
  Best regards,
  Your Company"""
      try:
          prompt = f"Write a personalized email for a company named {lead['name']} in {lead['location']} focusing on {lead['industry']}."
          payload = {"inputs": prompt}
          response = requests.post(API_URL, headers=headers, json=payload)
          return response.json()[0]['generated_text']
      except:
          return template.format(
              company_name=lead['name'],
              contact_name=lead.get('contact_name', 'Team'),
              industry=lead.get('industry', 'mining'),
              location=lead['location'],
              specific_need='equipment efficiency'
          )

  def get_leads():
      conn = sqlite3.connect('leads.db')
      df = pd.read_sql_query("SELECT * FROM scored_leads WHERE score > 10", conn)
      conn.close()
      return df

  if __name__ == "__main__":
      leads = get_leads()
      for _, lead in leads.iterrows():
          email_content = generate_email(lead)
          print(email_content)
  ```
- **Notes**: Hugging Face’s free tier has rate limits; use sparingly or rely on templates for initial testing.

### Step 5: Email Communication
- **Objective**: Send personalized emails to leads.
- **Tools**: SendGrid free tier (100 emails/day, [sendgrid.com/free/](https://sendgrid.com/free/)).
- **Actions**:
  1. **Set Up SendGrid**: Create an account and obtain an API key.
  2. **Integrate API**: Write a Python script to send emails.
  3. **Track Delivery**: Monitor delivery status within free tier limits.
- **Example Code**:
  ```python
  from sendgrid import SendGridAPIClient
  from sendgrid.helpers.mail import Mail
  import sqlite3
  import pandas as pd

  def send_email(to_email, content):
      message = Mail(
          from_email='<EMAIL>',
          to_emails=to_email,
          subject=content.split('\n')[0].replace('Subject: ', ''),
          plain_text_content=content
      )
      try:
          sg = SendGridAPIClient('YOUR_SENDGRID_API_KEY')
          response = sg.send(message)
          print(f"Email sent to {to_email}: {response.status_code}")
      except Exception as e:
          print(f"Error sending email to {to_email}: {e}")

  def get_emails():
      conn = sqlite3.connect('leads.db')
      df = pd.read_sql_query("SELECT email, email_content FROM scored_leads WHERE score > 10", conn)
      conn.close()
      return df

  if __name__ == "__main__":
      emails = get_emails()
      for _, row in emails.iterrows():
          send_email(row['email'], row['email_content'])
  ```
- **Notes**: Verify your email account with SendGrid to ensure delivery. Monitor usage to stay within the 100 emails/day limit.

### Step 6: Integration and Automation
- **Objective**: Orchestrate the workflow and automate execution.
- **Tools**: Python’s `schedule` library for automation.
- **Actions**:
  1. **Create Main Script**: Combine all components into a single script.
  2. **Schedule Execution**: Use `schedule` or cron jobs for daily runs.
- **Example Code**:
  ```python
  import time
  import schedule
  import lead_generation
  import lead_scoring
  import outreach_generation
  import email_communication

  def run_sdr_agent():
      url = "https://example.com/mining-directory"  # Replace with actual URL
      leads = lead_generation.scrape_leads(url)
      filtered_leads = lead_generation.filter_leads(leads)
      lead_generation.save_to_db(filtered_leads)
      scored_leads = lead_scoring.score_leads()
      for _, lead in scored_leads.iterrows():
          email_content = outreach_generation.generate_email(lead)
          # Assume email_content is saved to database
      emails = email_communication.get_emails()
      for _, row in emails.iterrows():
          email_communication.send_email(row['email'], row['email_content'])

  schedule.every().day.at("09:00").do(run_sdr_agent)

  while True:
      schedule.run_pending()
      time.sleep(1)
  ```
- **Notes**: Test the main script with a small dataset to ensure integration works smoothly.

### Step 7: Human Oversight
- **Objective**: Incorporate human review for critical tasks.
- **Actions**:
  - Manually review email responses and update lead statuses in the database.
  - Optionally, use Gmail’s API ([developers.google.com/gmail/api](https://developers.google.com/gmail/api)) to automate response categorization (e.g., based on keywords like “interested”).
  - Set up notifications via email or Slack’s free plan ([slack.com](https://slack.com/)) for high-priority leads.
- **Why**: Ensures quality control and handles complex interactions, aiming for 90% automation and 10% human intervention.

### Step 8: Testing and Iteration
- **Objective**: Validate and improve the agent’s performance.
- **Actions**:
  1. **Test Components**: Run each script individually with 10-20 leads to verify functionality.
  2. **Measure KPIs**: Track leads generated, emails sent, and response rates using pandas for analytics.
  3. **Iterate**: Refine scripts based on performance, adding features like response handling as needed.
- **Why**: Starting small ensures you stay within free tier limits and identify issues early.

## Low-Cost Alternatives
If free tiers are insufficient, consider:
- **Zapier**: $20/month for 750 tasks ([zapier.com](https://zapier.com/)).
- **Mailgun**: $5/month for 5,000 emails ([mailgun.com](https://mailgun.com/)).
- **Apollo.io**: $49/month for unlimited email credits ([apollo.io](https://apollo.io/)).

## Challenges and Solutions
- **Data Quality**: Cross-reference multiple sources and use Hunter.io for email verification.
- **Free Tier Limits**: Batch process leads to stay within limits (e.g., SendGrid’s 100 emails/day).
- **Scalability**: Optimize scripts for efficiency; consider low-cost plans for higher volumes.
- **Compliance**: Adhere to data privacy laws and website terms during scraping and emailing.

## Target Audience Considerations
The agent targets mining equipment suppliers, distributors, service companies, and mining companies with $10-50M revenue in regions like Africa and North America. Customize lead generation to focus on mining industry sources and tailor outreach with industry-specific language (e.g., referencing cobalt production for DRC-based companies).

## Conclusion
This guide enables you to build a functional AI SDR Agent using free tools and VS Code. By starting small, testing rigorously, and iterating based on performance, you can achieve an efficient and effective sales automation system. As your needs grow, low-cost tools can enhance scalability, but the free-tier approach meets your $0 budget while delivering core functionality.

**Citations**:
- [SendGrid Free Tier](https://sendgrid.com/free/)
- [Hugging Face Inference API](https://huggingface.co/docs/api-inference/index)
- [SQLite Documentation](https://www.sqlite.org/docs.html)
- [BeautifulSoup Documentation](https://www.crummy.com/software/BeautifulSoup/bs4/doc/)
- [Hunter.io](https://hunter.io/)
- [Git Documentation](https://git-scm.com/)
- [Gmail API](https://developers.google.com/gmail/api)
- [Slack](https://slack.com/)