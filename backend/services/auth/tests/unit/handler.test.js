const TestHelpers = require('../../../shared/utils/testHelpers');

// Setup test environment before importing modules
TestHelpers.setupTestEnvironment();

const { login, refresh, register, verifyToken } = require('../../handler');
const { UserService } = require('../../userService');

describe('Auth Handler', () => {
  let mockUserService;

  beforeEach(() => {
    TestHelpers.setupTestEnvironment();
    
    // Mock UserService
    mockUserService = {
      getUserByUsername: jest.fn(),
      createUser: jest.fn(),
      getUser: jest.fn()
    };
    
    // Replace the actual service with mock
    jest.doMock('../../userService', () => ({
      UserService: jest.fn(() => mockUserService)
    }));
  });

  afterEach(() => {
    TestHelpers.cleanupTestEnvironment();
  });

  describe('login', () => {
    it('should successfully login with valid credentials', async () => {
      const testUser = TestHelpers.generateTestUser({
        username: 'testuser',
        passwordHash: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.S' // 'password123'
      });

      mockUserService.getUserByUsername.mockResolvedValue(testUser);

      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'POST',
        body: JSON.stringify({
          username: 'testuser',
          password: 'password123'
        })
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await login(event, context);

      const body = TestHelpers.assertValidResponse(response, 200);
      expect(body.data).toHaveProperty('token');
      expect(body.data).toHaveProperty('user');
      expect(body.data.user.username).toBe('testuser');
      expect(mockUserService.getUserByUsername).toHaveBeenCalledWith('testuser');
    });

    it('should fail login with invalid username', async () => {
      mockUserService.getUserByUsername.mockResolvedValue(null);

      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'POST',
        body: JSON.stringify({
          username: 'nonexistent',
          password: 'password123'
        })
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await login(event, context);

      const body = TestHelpers.assertValidResponse(response, 401);
      expect(body.error.message).toBe('Invalid credentials');
      expect(mockUserService.getUserByUsername).toHaveBeenCalledWith('nonexistent');
    });

    it('should fail login with invalid password', async () => {
      const testUser = TestHelpers.generateTestUser({
        username: 'testuser',
        passwordHash: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PZvO.S' // 'password123'
      });

      mockUserService.getUserByUsername.mockResolvedValue(testUser);

      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'POST',
        body: JSON.stringify({
          username: 'testuser',
          password: 'wrongpassword'
        })
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await login(event, context);

      const body = TestHelpers.assertValidResponse(response, 401);
      expect(body.error.message).toBe('Invalid credentials');
    });

    it('should fail login with missing credentials', async () => {
      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'POST',
        body: JSON.stringify({
          username: 'testuser'
          // missing password
        })
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await login(event, context);

      const body = TestHelpers.assertValidResponse(response, 400);
      expect(body.error.message).toBe('Validation failed');
    });

    it('should fail login with invalid JSON', async () => {
      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'POST',
        body: 'invalid json'
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await login(event, context);

      const body = TestHelpers.assertValidResponse(response, 400);
      expect(body.error.message).toBe('Invalid JSON in request body');
    });
  });

  describe('register', () => {
    it('should successfully register a new user', async () => {
      const newUser = TestHelpers.generateTestUser({
        username: 'newuser',
        email: '<EMAIL>'
      });

      mockUserService.getUserByUsername.mockResolvedValue(null);
      mockUserService.getUserByEmail.mockResolvedValue(null);
      mockUserService.createUser.mockResolvedValue(newUser);

      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'POST',
        body: JSON.stringify({
          username: 'newuser',
          email: '<EMAIL>',
          password: 'Password123!',
          role: 'user'
        })
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await register(event, context);

      const body = TestHelpers.assertValidResponse(response, 201);
      expect(body.data.user.username).toBe('newuser');
      expect(body.data.user.email).toBe('<EMAIL>');
      expect(mockUserService.createUser).toHaveBeenCalled();
    });

    it('should fail registration with existing username', async () => {
      const existingUser = TestHelpers.generateTestUser();
      mockUserService.getUserByUsername.mockResolvedValue(existingUser);

      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'POST',
        body: JSON.stringify({
          username: 'existinguser',
          email: '<EMAIL>',
          password: 'Password123!',
          role: 'user'
        })
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await register(event, context);

      const body = TestHelpers.assertValidResponse(response, 400);
      expect(body.error.message).toBe('Username already exists');
    });

    it('should fail registration with weak password', async () => {
      mockUserService.getUserByUsername.mockResolvedValue(null);
      mockUserService.getUserByEmail.mockResolvedValue(null);

      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'POST',
        body: JSON.stringify({
          username: 'newuser',
          email: '<EMAIL>',
          password: 'weak', // Too weak
          role: 'user'
        })
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await register(event, context);

      const body = TestHelpers.assertValidResponse(response, 400);
      expect(body.error.message).toBe('Validation failed');
    });
  });

  describe('refresh', () => {
    it('should successfully refresh a valid token', async () => {
      const testUser = TestHelpers.generateTestUser();
      mockUserService.getUser.mockResolvedValue(testUser);

      // Generate a valid token first
      const jwt = require('jsonwebtoken');
      const token = jwt.sign(
        { id: testUser.userId, username: testUser.username },
        process.env.JWT_SECRET,
        { expiresIn: '1h' }
      );

      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'POST',
        body: JSON.stringify({ token })
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await refresh(event, context);

      const body = TestHelpers.assertValidResponse(response, 200);
      expect(body.data).toHaveProperty('token');
      expect(body.data).toHaveProperty('user');
    });

    it('should fail refresh with invalid token', async () => {
      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'POST',
        body: JSON.stringify({ token: 'invalid.token.here' })
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await refresh(event, context);

      const body = TestHelpers.assertValidResponse(response, 401);
      expect(body.error.message).toBe('Invalid or expired token');
    });

    it('should fail refresh with missing token', async () => {
      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'POST',
        body: JSON.stringify({})
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await refresh(event, context);

      const body = TestHelpers.assertValidResponse(response, 400);
      expect(body.error.message).toBe('Validation failed');
    });
  });

  describe('verifyToken', () => {
    it('should successfully verify a valid token', async () => {
      const testUser = TestHelpers.generateTestUser();
      mockUserService.getUser.mockResolvedValue(testUser);

      // Generate a valid token
      const jwt = require('jsonwebtoken');
      const token = jwt.sign(
        { id: testUser.userId, username: testUser.username },
        process.env.JWT_SECRET,
        { expiresIn: '1h' }
      );

      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'GET',
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await verifyToken(event, context);

      const body = TestHelpers.assertValidResponse(response, 200);
      expect(body.data.valid).toBe(true);
      expect(body.data.user.username).toBe(testUser.username);
    });

    it('should fail verification with missing authorization header', async () => {
      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'GET',
        headers: {}
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await verifyToken(event, context);

      const body = TestHelpers.assertValidResponse(response, 401);
      expect(body.error.message).toBe('Authorization header is required');
    });

    it('should fail verification with inactive user', async () => {
      const testUser = TestHelpers.generateTestUser({ status: 'inactive' });
      mockUserService.getUser.mockResolvedValue(testUser);

      const jwt = require('jsonwebtoken');
      const token = jwt.sign(
        { id: testUser.userId, username: testUser.username },
        process.env.JWT_SECRET,
        { expiresIn: '1h' }
      );

      const event = TestHelpers.generateLambdaEvent({
        httpMethod: 'GET',
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      const context = TestHelpers.generateLambdaContext();
      const response = await verifyToken(event, context);

      const body = TestHelpers.assertValidResponse(response, 401);
      expect(body.error.message).toBe('User account is not active');
    });
  });
});
