const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();
const TABLE_NAME = process.env.LEADS_TABLE;

class Lead {
  static async create(leadData) {
    const params = {
      TableName: TABLE_NAME,
      Item: {
        leadId: leadData.leadId || AWS.util.uuid.v4(),
        companyName: leadData.companyName,
        contactEmail: leadData.contactEmail,
        revenue: leadData.revenue,
        expansionPlans: leadData.expansionPlans,
        score: leadData.score || 0,
        outreachMessage: leadData.outreachMessage || '',
        emailStatus: leadData.emailStatus || 'pending',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    };

    await dynamodb.put(params).promise();
    return params.Item;
  }

  static async get(leadId) {
    const params = {
      TableName: TABLE_NAME,
      Key: { leadId }
    };
    const result = await dynamodb.get(params).promise();
    return result.Item;
  }

  static async update(leadId, updates) {
    const attributeUpdates = {};
    const expressionAttributeValues = {};
    let updateExpression = 'SET updatedAt = :updatedAt';

    expressionAttributeValues[':updatedAt'] = new Date().toISOString();

    for (const [key, value] of Object.entries(updates)) {
      attributeUpdates[`#${key}`] = key;
      expressionAttributeValues[`:${key}`] = value;
      updateExpression += `, #${key} = :${key}`;
    }

    const params = {
      TableName: TABLE_NAME,
      Key: { leadId },
      UpdateExpression: updateExpression,
      ExpressionAttributeNames: attributeUpdates,
      ExpressionAttributeValues: expressionAttributeValues,
      ReturnValues: 'ALL_NEW'
    };

    const result = await dynamodb.update(params).promise();
    return result.Attributes;
  }
}

module.exports = Lead;