#!/bin/bash

# AI SDR Deployment Script
# Comprehensive deployment for staging and production environments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENTS=("dev" "staging" "prod")
REQUIRED_ENV_VARS=("AWS_REGION" "AWS_PROFILE")

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if serverless is installed
    if ! command -v serverless &> /dev/null; then
        log_error "Serverless Framework is not installed. Please install it first:"
        echo "npm install -g serverless"
        exit 1
    fi
    
    # Check if AWS CLI is installed
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Check Node.js version
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js version 18 or higher is required. Current version: $(node --version)"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Validate environment
validate_environment() {
    local env=$1
    
    if [[ ! " ${ENVIRONMENTS[@]} " =~ " ${env} " ]]; then
        log_error "Invalid environment: $env. Valid environments: ${ENVIRONMENTS[*]}"
        exit 1
    fi
    
    log_info "Validating environment: $env"
    
    # Check required environment variables
    for var in "${REQUIRED_ENV_VARS[@]}"; do
        if [ -z "${!var}" ]; then
            log_error "Required environment variable $var is not set"
            exit 1
        fi
    done
    
    log_success "Environment validation passed"
}

# Install dependencies
install_dependencies() {
    log_info "Installing dependencies..."
    
    if [ ! -d "node_modules" ]; then
        npm install
    else
        log_info "Dependencies already installed"
    fi
    
    log_success "Dependencies installed"
}

# Run tests
run_tests() {
    log_info "Running tests..."
    
    npm test
    
    if [ $? -eq 0 ]; then
        log_success "All tests passed"
    else
        log_error "Tests failed. Deployment aborted."
        exit 1
    fi
}

# Deploy to environment
deploy_to_environment() {
    local env=$1
    
    log_info "Deploying to $env environment..."
    
    # Set environment-specific configurations
    export NODE_ENV=$env
    
    # Deploy using serverless
    serverless deploy --stage $env --verbose
    
    if [ $? -eq 0 ]; then
        log_success "Deployment to $env completed successfully"
        
        # Get deployment info
        log_info "Getting deployment information..."
        serverless info --stage $env
        
        # Run post-deployment tests
        if [ "$env" != "prod" ]; then
            log_info "Running post-deployment health checks..."
            run_health_checks $env
        fi
        
    else
        log_error "Deployment to $env failed"
        exit 1
    fi
}

# Run health checks
run_health_checks() {
    local env=$1
    
    log_info "Running health checks for $env environment..."
    
    # Get the API Gateway URL
    API_URL=$(serverless info --stage $env | grep "ServiceEndpoint" | awk '{print $2}')
    
    if [ -n "$API_URL" ]; then
        # Test health endpoint
        HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$API_URL/health")
        
        if [ "$HTTP_STATUS" -eq 200 ]; then
            log_success "Health check passed"
        else
            log_warning "Health check failed with status: $HTTP_STATUS"
        fi
    else
        log_warning "Could not determine API URL for health checks"
    fi
}

# Rollback deployment
rollback_deployment() {
    local env=$1
    
    log_warning "Rolling back deployment for $env environment..."
    
    # This would typically involve reverting to the previous version
    # For now, we'll just log the action
    log_info "Rollback functionality would be implemented here"
    log_info "You can manually rollback using: serverless rollback --timestamp <timestamp> --stage $env"
}

# Main deployment function
main() {
    local env=${1:-dev}
    local skip_tests=${2:-false}
    
    echo "=========================================="
    echo "AI SDR Deployment Script"
    echo "Environment: $env"
    echo "Skip Tests: $skip_tests"
    echo "=========================================="
    
    # Check prerequisites
    check_prerequisites
    
    # Validate environment
    validate_environment $env
    
    # Install dependencies
    install_dependencies
    
    # Run tests (unless skipped)
    if [ "$skip_tests" != "true" ]; then
        run_tests
    else
        log_warning "Skipping tests as requested"
    fi
    
    # Deploy
    deploy_to_environment $env
    
    log_success "Deployment process completed!"
    echo "=========================================="
}

# Handle script arguments
case "${1:-help}" in
    "dev"|"staging"|"prod")
        main $1 $2
        ;;
    "rollback")
        if [ -z "$2" ]; then
            log_error "Environment required for rollback. Usage: $0 rollback <environment>"
            exit 1
        fi
        rollback_deployment $2
        ;;
    "health")
        if [ -z "$2" ]; then
            log_error "Environment required for health check. Usage: $0 health <environment>"
            exit 1
        fi
        run_health_checks $2
        ;;
    "help"|*)
        echo "AI SDR Deployment Script"
        echo ""
        echo "Usage:"
        echo "  $0 <environment> [skip-tests]  Deploy to environment"
        echo "  $0 rollback <environment>      Rollback deployment"
        echo "  $0 health <environment>        Run health checks"
        echo "  $0 help                        Show this help"
        echo ""
        echo "Environments: dev, staging, prod"
        echo ""
        echo "Examples:"
        echo "  $0 dev                         Deploy to development"
        echo "  $0 staging                     Deploy to staging with tests"
        echo "  $0 prod true                   Deploy to production, skip tests"
        echo "  $0 rollback staging            Rollback staging deployment"
        echo "  $0 health prod                 Check production health"
        ;;
esac
