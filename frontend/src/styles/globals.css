@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }
  
  .btn-primary {
    @apply btn bg-blue-600 text-white hover:bg-blue-700 h-10 py-2 px-4;
  }
  
  .btn-secondary {
    @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300 h-10 py-2 px-4;
  }
  
  .btn-outline {
    @apply btn border border-gray-300 bg-transparent hover:bg-gray-50 h-10 py-2 px-4;
  }
  
  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 h-10 py-2 px-4;
  }
  
  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .card {
    @apply rounded-lg border border-gray-200 bg-white text-gray-950 shadow-sm;
  }
  
  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }
  
  .card-title {
    @apply text-2xl font-semibold leading-none tracking-tight;
  }
  
  .card-description {
    @apply text-sm text-gray-500;
  }
  
  .card-content {
    @apply p-6 pt-0;
  }
  
  .card-footer {
    @apply flex items-center p-6 pt-0;
  }
  
  .badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }
  
  .badge-default {
    @apply badge border-transparent bg-gray-900 text-gray-50 hover:bg-gray-900/80;
  }
  
  .badge-secondary {
    @apply badge border-transparent bg-gray-100 text-gray-900 hover:bg-gray-100/80;
  }
  
  .badge-success {
    @apply badge border-transparent bg-green-500 text-white hover:bg-green-500/80;
  }
  
  .badge-warning {
    @apply badge border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80;
  }
  
  .badge-danger {
    @apply badge border-transparent bg-red-500 text-white hover:bg-red-500/80;
  }
  
  .table {
    @apply w-full caption-bottom text-sm;
  }
  
  .table-header {
    @apply border-b;
  }
  
  .table-body {
    @apply [&_tr:last-child]:border-0;
  }
  
  .table-row {
    @apply border-b transition-colors hover:bg-gray-50/50 data-[state=selected]:bg-gray-50;
  }
  
  .table-head {
    @apply h-12 px-4 text-left align-middle font-medium text-gray-500 [&:has([role=checkbox])]:pr-0;
  }
  
  .table-cell {
    @apply p-4 align-middle [&:has([role=checkbox])]:pr-0;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
