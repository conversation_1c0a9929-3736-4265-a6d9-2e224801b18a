const Joi = require('joi');
const {
  lambdaWrapper,
  parseBody,
  getPathParameters,
  getQueryParameters,
  successResponse,
  errorResponse,
  validateInput,
  ValidationError,
  logger
} = require('../shared/utils/api');
const { leadService } = require('../shared/models/lead');
const { LeadEnrichmentClient, DEFAULT_MINING_SOURCES } = require('../shared/utils/leadEnrichment');

// Validation schemas
const importLeadsSchema = Joi.object({
  leads: Joi.array().items(Joi.object({
    companyName: Joi.string().required(),
    contactEmail: Joi.string().email().required(),
    contactName: Joi.string().optional(),
    phone: Joi.string().optional(),
    revenue: Joi.number().positive().optional(),
    industry: Joi.string().optional(),
    location: Joi.string().optional(),
    expansionPlans: Joi.string().optional(),
    source: Joi.string().optional(),
    tags: Joi.array().items(Joi.string()).optional()
  })).required().min(1)
});

const updateLeadSchema = Joi.object({
  companyName: Joi.string().optional(),
  contactEmail: Joi.string().email().optional(),
  contactName: Joi.string().optional(),
  phone: Joi.string().optional(),
  revenue: Joi.number().positive().optional(),
  industry: Joi.string().optional(),
  location: Joi.string().optional(),
  expansionPlans: Joi.string().optional(),
  score: Joi.number().min(0).max(100).optional(),
  outreachMessage: Joi.string().optional(),
  emailStatus: Joi.string().valid('pending', 'sent', 'opened', 'replied', 'bounced').optional(),
  tags: Joi.array().items(Joi.string()).optional(),
  notes: Joi.string().optional(),
  assignedTo: Joi.string().optional()
});

const importLeads = async (event) => {
  const body = parseBody(event);
  const validatedData = validateInput(body, importLeadsSchema);

  const { leads } = validatedData;

  logger.info('Importing leads', { count: leads.length });

  try {
    const importedLeads = [];
    const errors = [];

    // Process each lead
    for (let i = 0; i < leads.length; i++) {
      try {
        const lead = await leadService.createLead(leads[i]);
        importedLeads.push(lead);
      } catch (error) {
        errors.push({
          index: i,
          lead: leads[i],
          error: error.message
        });
      }
    }

    logger.info('Lead import completed', {
      imported: importedLeads.length,
      errors: errors.length
    });

    return successResponse({
      message: 'Lead import completed',
      imported: importedLeads.length,
      errors: errors.length,
      leads: importedLeads,
      importErrors: errors
    });
  } catch (error) {
    logger.error('Lead import failed', { error: error.message });
    throw error;
  }
};

const getLeads = async (event) => {
  const queryParams = getQueryParameters(event);

  // Parse query parameters
  const options = {
    page: parseInt(queryParams.page) || 1,
    limit: parseInt(queryParams.limit) || 20,
    sort: queryParams.sort || 'createdAt',
    order: queryParams.order || 'desc',
    filter: {}
  };

  // Add filters
  if (queryParams.status) options.filter.emailStatus = queryParams.status;
  if (queryParams.industry) options.filter.industry = queryParams.industry;
  if (queryParams.assignedTo) options.filter.assignedTo = queryParams.assignedTo;
  if (queryParams.search) options.filter.companyName = `*${queryParams.search}*`;

  logger.info('Fetching leads', options);

  try {
    const result = await leadService.getLeads(options);

    return successResponse(result);
  } catch (error) {
    logger.error('Failed to fetch leads', { error: error.message });
    throw error;
  }
};

const getLeadById = async (event) => {
  const { id } = getPathParameters(event);

  if (!id) {
    throw new ValidationError('Lead ID is required');
  }

  logger.info('Fetching lead by ID', { leadId: id });

  try {
    const lead = await leadService.getLead(id);

    return successResponse(lead);
  } catch (error) {
    logger.error('Failed to fetch lead', { leadId: id, error: error.message });
    throw error;
  }
};

const updateLead = async (event) => {
  const { id } = getPathParameters(event);
  const body = parseBody(event);

  if (!id) {
    throw new ValidationError('Lead ID is required');
  }

  const validatedUpdates = validateInput(body, updateLeadSchema);

  logger.info('Updating lead', { leadId: id, updates: Object.keys(validatedUpdates) });

  try {
    const updatedLead = await leadService.updateLead(id, validatedUpdates);

    return successResponse(updatedLead);
  } catch (error) {
    logger.error('Failed to update lead', { leadId: id, error: error.message });
    throw error;
  }
};

const deleteLead = async (event) => {
  const { id } = getPathParameters(event);

  if (!id) {
    throw new ValidationError('Lead ID is required');
  }

  logger.info('Deleting lead', { leadId: id });

  try {
    await leadService.deleteLead(id);

    return successResponse({ message: 'Lead deleted successfully' }, 204);
  } catch (error) {
    logger.error('Failed to delete lead', { leadId: id, error: error.message });
    throw error;
  }
};

const createLead = async (event) => {
  const body = parseBody(event);

  logger.info('Creating new lead');

  try {
    const lead = await leadService.createLead(body);

    return successResponse(lead, 201);
  } catch (error) {
    logger.error('Failed to create lead', { error: error.message });
    throw error;
  }
};

// Main handler for API Gateway routing
const main = lambdaWrapper(async (event, context) => {
  const { httpMethod, path, pathParameters } = event;
  const pathSegments = path.split('/').filter(Boolean);
  const action = pathSegments[pathSegments.length - 1];
  const leadId = pathParameters?.id;

  logger.info('Leads service request', { httpMethod, path, action, leadId });

  switch (httpMethod) {
    case 'GET':
      if (leadId) {
        return await getLeadById(event, context);
      } else {
        return await getLeads(event, context);
      }
    case 'POST':
      if (action === 'import') {
        return await importLeads(event, context);
      } else {
        return await createLead(event, context);
      }
    case 'PUT':
    case 'PATCH':
      if (!leadId) {
        throw new ValidationError('Lead ID is required for updates');
      }
      return await updateLead(event, context);
    case 'DELETE':
      if (!leadId) {
        throw new ValidationError('Lead ID is required for deletion');
      }
      return await deleteLead(event, context);
    case 'OPTIONS':
      return successResponse({ message: 'CORS preflight' });
    default:
      throw new ValidationError(`Unsupported HTTP method: ${httpMethod}`);
  }
});

// Export wrapped handlers
// Lead Enrichment Functions
const enrichmentClient = new LeadEnrichmentClient();

/**
 * Discover leads from mining industry sources
 */
const discoverLeads = async (event) => {
  try {
    const body = parseBody(event);
    const { sources = DEFAULT_MINING_SOURCES, criteria, limit = 50 } = body;

    logger.info('Starting lead discovery process');

    // Check if enrichment service is available
    await enrichmentClient.healthCheck();

    // Discover and enrich leads
    const results = await enrichmentClient.discoverAndEnrichLeads({
      sources,
      criteria,
      limit
    });

    // Import high-quality leads into our main database
    if (results.final_leads && results.final_leads.length > 0) {
      const leadsToImport = results.final_leads.map(lead => ({
        companyName: lead.name || lead.company,
        contactEmail: lead.email,
        contactName: lead.contact_name || 'Unknown',
        phone: lead.phone,
        revenue: lead.revenue ? parseFloat(lead.revenue.replace(/[^\d.]/g, '')) : null,
        industry: lead.industry || 'mining',
        location: lead.location,
        source: `enrichment:${lead.source_url}`,
        tags: ['enriched', 'high-quality'],
        leadScore: lead.lead_score || 0,
        confidence: lead.confidence_score || 0.5
      }));

      // Import leads using existing service
      for (const leadData of leadsToImport) {
        try {
          await leadService.createLead(leadData);
        } catch (error) {
          logger.warn(`Failed to import lead ${leadData.companyName}:`, error.message);
        }
      }
    }

    return successResponse({
      message: 'Lead discovery completed successfully',
      summary: {
        sources_processed: results.sources_processed,
        total_scraped: results.total_scraped,
        total_enriched: results.total_enriched,
        imported_to_main_db: results.final_leads?.length || 0,
        errors: results.errors
      },
      discovered_leads: results.final_leads
    });

  } catch (error) {
    logger.error('Lead discovery failed:', error);
    return errorResponse(error.message, 500);
  }
};

/**
 * Enrich existing leads with additional data
 */
const enrichExistingLeads = async (event) => {
  try {
    const body = parseBody(event);
    const { leadIds, criteria } = body;

    if (!leadIds || !Array.isArray(leadIds)) {
      throw new ValidationError('leadIds array is required');
    }

    logger.info(`Enriching ${leadIds.length} existing leads`);

    // Get leads from our database
    const leads = [];
    for (const leadId of leadIds) {
      try {
        const lead = await leadService.getLeadById(leadId);
        if (lead) {
          leads.push({
            name: lead.companyName,
            email: lead.contactEmail,
            company: lead.companyName,
            phone: lead.phone,
            location: lead.location,
            industry: lead.industry,
            revenue: lead.revenue?.toString()
          });
        }
      } catch (error) {
        logger.warn(`Failed to get lead ${leadId}:`, error.message);
      }
    }

    if (leads.length === 0) {
      return successResponse({
        message: 'No valid leads found for enrichment',
        enriched_count: 0
      });
    }

    // Enrich leads using Python service
    const enrichmentResult = await enrichmentClient.enrichLeads(leads, criteria);

    // Update leads in our database with enriched data
    let updatedCount = 0;
    if (enrichmentResult.enriched_leads) {
      for (let i = 0; i < enrichmentResult.enriched_leads.length; i++) {
        const enrichedLead = enrichmentResult.enriched_leads[i];
        const originalLeadId = leadIds[i];

        try {
          const updateData = {
            leadScore: enrichedLead.lead_score || 0,
            confidence: enrichedLead.confidence_score || 0.5,
            tags: ['enriched']
          };

          // Add any new contact information found
          if (enrichedLead.emails && enrichedLead.emails.length > 0) {
            updateData.contactEmail = enrichedLead.emails[0];
          }
          if (enrichedLead.phone && !updateData.phone) {
            updateData.phone = enrichedLead.phone;
          }

          await leadService.updateLead(originalLeadId, updateData);
          updatedCount++;
        } catch (error) {
          logger.warn(`Failed to update lead ${originalLeadId}:`, error.message);
        }
      }
    }

    return successResponse({
      message: 'Lead enrichment completed',
      leads_processed: leads.length,
      leads_enriched: enrichmentResult.leads_enriched,
      leads_updated: updatedCount,
      enrichment_details: enrichmentResult
    });

  } catch (error) {
    logger.error('Lead enrichment failed:', error);
    return errorResponse(error.message, 500);
  }
};

/**
 * Get enrichment service status and sources
 */
const getEnrichmentStatus = async (event) => {
  try {
    // Check service health
    const health = await enrichmentClient.healthCheck();

    // Get available sources
    const sources = await enrichmentClient.getScrapingSources();

    return successResponse({
      service_status: health,
      available_sources: sources,
      default_sources: DEFAULT_MINING_SOURCES
    });

  } catch (error) {
    logger.error('Failed to get enrichment status:', error);
    return errorResponse(error.message, 500);
  }
};

module.exports = {
  main,
  importLeads: lambdaWrapper(importLeads),
  getLeads: lambdaWrapper(getLeads),
  getLeadById: lambdaWrapper(getLeadById),
  updateLead: lambdaWrapper(updateLead),
  deleteLead: lambdaWrapper(deleteLead),
  createLead: lambdaWrapper(createLead),
  discoverLeads: lambdaWrapper(discoverLeads),
  enrichExistingLeads: lambdaWrapper(enrichExistingLeads),
  getEnrichmentStatus: lambdaWrapper(getEnrichmentStatus)
};