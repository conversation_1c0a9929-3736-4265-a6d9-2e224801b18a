const sgMail = require('@sendgrid/mail');
const Joi = require('joi');
const { logger, ValidationError, InternalServerError } = require('./api');
const { configService } = require('./config');
const { retryWithBackoff } = require('./helpers');

// Email validation schema
const emailSchema = Joi.object({
  to: Joi.alternatives().try(
    Joi.string().email(),
    Joi.array().items(Joi.string().email()).min(1)
  ).required(),
  subject: Joi.string().required().min(1).max(255),
  html: Joi.string().optional(),
  text: Joi.string().optional(),
  templateId: Joi.string().optional(),
  templateData: Joi.object().optional(),
  attachments: Joi.array().items(Joi.object({
    filename: Joi.string().required(),
    content: Joi.string().required(),
    type: Joi.string().optional(),
    disposition: Joi.string().optional()
  })).optional(),
  from: Joi.string().email().optional(),
  fromName: Joi.string().optional(),
  replyTo: Joi.string().email().optional(),
  tags: Joi.array().items(Joi.string()).optional(),
  trackOpens: Joi.boolean().default(true),
  trackClicks: Joi.boolean().default(true)
});

class EmailService {
  constructor() {
    this.initialized = false;
    this.config = null;
  }

  /**
   * Initialize the email service with configuration
   */
  async initialize() {
    if (this.initialized) {
      return;
    }

    try {
      this.config = await configService.getEmailConfig();
      sgMail.setApiKey(this.config.sendgridApiKey);
      this.initialized = true;

      logger.info('Email service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize email service', { error: error.message });
      throw new InternalServerError('Email service initialization failed');
    }
  }

  /**
   * Send email with validation and retry logic
   * @param {Object} emailData - Email data
   * @returns {Promise<Object>} Send result
   */
  async sendEmail(emailData) {
    await this.initialize();

    // Validate input
    const { error, value } = emailSchema.validate(emailData);
    if (error) {
      throw new ValidationError('Invalid email data', error.details);
    }

    const {
      to,
      subject,
      html,
      text,
      templateId,
      templateData,
      attachments,
      from,
      fromName,
      replyTo,
      tags,
      trackOpens,
      trackClicks
    } = value;

    // Build email message
    const msg = {
      to: Array.isArray(to) ? to : [to],
      subject,
      from: {
        email: from || this.config.fromEmail,
        name: fromName || this.config.fromName
      }
    };

    // Add content
    if (templateId) {
      msg.templateId = templateId;
      if (templateData) {
        msg.dynamicTemplateData = templateData;
      }
    } else {
      if (html) msg.html = html;
      if (text) msg.text = text;

      if (!html && !text) {
        throw new ValidationError('Either html, text, or templateId must be provided');
      }
    }

    // Add optional fields
    if (replyTo) msg.replyTo = replyTo;
    if (attachments) msg.attachments = attachments;
    if (tags) msg.categories = tags;

    // Add tracking settings
    msg.trackingSettings = {
      clickTracking: { enable: trackClicks },
      openTracking: { enable: trackOpens }
    };

    try {
      logger.info('Sending email', {
        to: Array.isArray(to) ? to.length : 1,
        subject,
        templateId: templateId || 'custom'
      });

      // Send with retry logic
      const result = await retryWithBackoff(
        () => sgMail.send(msg),
        3, // max retries
        1000 // base delay
      );

      logger.info('Email sent successfully', {
        messageId: result[0]?.headers?.['x-message-id'],
        to: Array.isArray(to) ? to.length : 1
      });

      return {
        success: true,
        messageId: result[0]?.headers?.['x-message-id'],
        to: msg.to,
        subject
      };
    } catch (error) {
      logger.error('Failed to send email', {
        error: error.message,
        code: error.code,
        to: Array.isArray(to) ? to.length : 1,
        subject
      });

      // Handle specific SendGrid errors
      if (error.code === 401) {
        throw new InternalServerError('Email service authentication failed');
      } else if (error.code === 403) {
        throw new InternalServerError('Email service access forbidden');
      } else if (error.code >= 400 && error.code < 500) {
        throw new ValidationError(`Email service error: ${error.message}`);
      }

      throw new InternalServerError('Failed to send email');
    }
  }

  /**
   * Send bulk emails
   * @param {Array} emails - Array of email data objects
   * @returns {Promise<Object>} Bulk send result
   */
  async sendBulkEmails(emails) {
    await this.initialize();

    if (!Array.isArray(emails) || emails.length === 0) {
      throw new ValidationError('Emails array is required and must not be empty');
    }

    logger.info('Sending bulk emails', { count: emails.length });

    const results = {
      sent: [],
      failed: []
    };

    // Process emails in batches to avoid rate limits
    const batchSize = 10;
    for (let i = 0; i < emails.length; i += batchSize) {
      const batch = emails.slice(i, i + batchSize);

      const batchPromises = batch.map(async (emailData, index) => {
        try {
          const result = await this.sendEmail(emailData);
          results.sent.push({ index: i + index, result });
        } catch (error) {
          results.failed.push({
            index: i + index,
            email: emailData,
            error: error.message
          });
        }
      });

      await Promise.all(batchPromises);

      // Add delay between batches to respect rate limits
      if (i + batchSize < emails.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    logger.info('Bulk email send completed', {
      total: emails.length,
      sent: results.sent.length,
      failed: results.failed.length
    });

    return results;
  }

  /**
   * Validate email template
   * @param {string} templateId - Template ID
   * @param {Object} templateData - Template data
   * @returns {Promise<boolean>} Validation result
   */
  async validateTemplate(templateId, templateData = {}) {
    await this.initialize();

    try {
      // SendGrid doesn't have a direct template validation API
      // This is a placeholder for template validation logic
      logger.info('Validating email template', { templateId });

      return true;
    } catch (error) {
      logger.error('Template validation failed', {
        templateId,
        error: error.message
      });
      return false;
    }
  }
}

// Export singleton instance
const emailService = new EmailService();

module.exports = {
  EmailService,
  emailService,
  // Legacy export for backward compatibility
  sendEmail: (to, subject, html) => emailService.sendEmail({ to, subject, html })
};